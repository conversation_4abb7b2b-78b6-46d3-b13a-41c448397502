import json
import os
import threading
import time
import logging
import argparse
from logger_config import setup_logger
from DingRobot import DingRobotHandler
from LarkRobot import LarkRobot
from IBHelper import IBHelper
from dingtalk_stream import DingTalkStreamClient, Credential
from dingtalk_stream import ChatbotMessage

def start_ding_robot(config):
    """启动钉钉机器人"""
    try:
        # 创建并启动钉钉机器人
        credential = Credential(
            client_id=config['dingtalk']['client_id'],
            client_secret=config['dingtalk']['client_secret']
        )
        client = DingTalkStreamClient(credential)
        handler = DingRobotHandler(config=config)
        client.register_callback_handler(ChatbotMessage.TOPIC, handler)
        
        client.start_forever()

    except Exception as e:
        logging.error(f"钉钉机器人运行时错误: {e}")

def start_lark_robot(config):
    """启动飞书机器人"""
    try:
        # 创建并启动飞书机器人
        handler = LarkRobot(config=config)
        handler.start()
        
    except Exception as e:
        logging.error(f"飞书机器人运行时错误: {e}")

def start_ib_helper(config):
    """启动IBHelper服务"""
    try:
        # 创建IBHelper实例
        helper = IBHelper(config=config)
        helper.connect()
        time.sleep(1)
        
        helper.load_contracts("Contracts")
        
        # 启动IB API线程
        api_thread = threading.Thread(target=helper.run, daemon=True)
        api_thread.start()
        
        # 订阅市场数据
        helper.subscribe_market_data(helper.contracts)
        
        return helper
        
    except Exception as e:
        logging.error(f"IBHelper运行时错误: {e}")
        return None

def main():
    # 使用统一的日志设置
    logger = setup_logger("TradingRobot")
    logger.info("启动交易机器人系统...")

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='启动交易机器人系统')
    parser.add_argument('--ding', action='store_true', help='启动钉钉机器人')
    parser.add_argument('--lark', action='store_true', help='启动飞书机器人')
    parser.add_argument('--ib', action='store_true', help='启动IBHelper')
    parser.add_argument('--all', action='store_true', help='启动所有服务')
    args = parser.parse_args()

    # 如果没有指定任何参数，默认启动所有服务
    if not (args.ding or args.lark or args.ib or args.all):
        args.all = True

    # 加载配置文件
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
            logger.info("已加载配置文件: config.json")
    except FileNotFoundError:
        logger.warning("配置文件不存在，使用默认配置")
        config = {
            'dingtalk': {
                'client_id': "dingehr9xf9xuhugsxvx",
                'client_secret': "SDGNzjyZ4c1uUXVrjjMhnSLC4ke_rYgE1hTeiSOKDR0aLFSvUm6EMIXvYSYiSLb5",
                'webhook': {
                    'url': 'https://oapi.dingtalk.com/robot/send?access_token=f9b2bc4c0ef7754e9e61f7fe2cc357505d1fc64ecb60cce6700b6c3fa3068468',
                    'secret': "SECe580ed83f2cbcf474f987c051cce5ec80aec71a04ca69541a44d80911ae8b402"
                },
                'port': 5556,
                'topic': 'ding'
            },
            'lark': {
                'app_id': "cli_a76ceec12d30100e",
                'app_secret': "rwASPEm1AmZVhOdIE1PfshY2Xbp6PP6G",
                'open_id': "ou_a17afae1032f5ec0283ca719f4c79311",
                'port': 5557,
                'topic': 'lark'
            }
        }
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return

    # 创建线程列表
    threads = []
    ib_helper = None

    # 启动所选服务
    if args.all or args.ding:
        ding_thread = threading.Thread(target=start_ding_robot, args=(config,), daemon=True)
        ding_thread.start()
        threads.append(ding_thread)
        logger.info("钉钉机器人线程已启动")

    if args.all or args.lark:
        lark_thread = threading.Thread(target=start_lark_robot, args=(config,), daemon=True)
        lark_thread.start()
        threads.append(lark_thread)
        logger.info("飞书机器人线程已启动")

    if args.all or args.ib:
        ib_helper = start_ib_helper(config)
        logger.info("IBHelper已启动")

    # 主循环
    try:
        logger.info("所有服务已启动，按Ctrl+C退出")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("检测到Ctrl+C，正在关闭服务...")
        
        # 关闭IBHelper
        if ib_helper:
            try:
                ib_helper.unsubscribe_market_data()
                ib_helper.app.disconnect()
                logger.info("IBHelper已关闭")
            except Exception as e:
                logger.error(f"关闭IBHelper时出错: {e}")
        
        logger.info("所有服务已关闭")

if __name__ == '__main__':
    main()