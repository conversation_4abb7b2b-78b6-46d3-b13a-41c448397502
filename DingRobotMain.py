import json
import os
from DingRobot import Ding<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,setup_logger
from dingtalk_stream import DingTalkStreamClient, Credential
from dingtalk_stream import ChatbotMessage

def main():
    logger = setup_logger()
    
    logger.info("启动钉钉机器人...")

    # 加载配置文件
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        config = {
            'dingtalk': {
                'client_id': "dingehr9xf9xuhugsxvx",
                'client_secret': "SDGNzjyZ4c1uUXVrjjMhnSLC4ke_rYgE1hTeiSOKDR0aLFSvUm6EMIXvYSYiSLb5",
                'webhook': {
                    'url': 'https://oapi.dingtalk.com/robot/send?access_token=f9b2bc4c0ef7754e9e61f7fe2cc357505d1fc64ecb60cce6700b6c3fa3068468',
                    'secret': "SECe580ed83f2cbcf474f987c051cce5ec80aec71a04ca69541a44d80911ae8b402"
                },
                'port': 5556
            }
        }

    # 钉钉机器人配置
    credential = Credential(
        config['dingtalk']['client_id'],
        config['dingtalk']['client_secret']
    )

    try:
        # 创建并启动钉钉机器人
        client = DingTalkStreamClient(credential)
        handler = DingRobotHandler(logger, config)  # 传入webhook实例
        client.register_callback_handler(ChatbotMessage.TOPIC, handler)
        
        logger.info("钉钉机器人启动中...")
        client.start_forever()

    except KeyboardInterrupt:
        logger.info("正在关闭机器人...")
    except Exception as e:
        logger.error(f"运行时错误: {e}")
    finally:
        # 清理资源
        logger.info("机器人已关闭")


if __name__ == '__main__':
    main()