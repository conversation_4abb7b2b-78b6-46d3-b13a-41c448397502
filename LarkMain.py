import json
import logging
from LarkRobotWS import LarkRobotWS, setup_logger
import time

# 设置日志
logger = setup_logger()

def main():
    # 从配置文件加载
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        config = {
            'lark': {
                'app_id': "cli_a76ceec12d30100e",
                'app_secret': "rwASPEm1AmZVhOdIE1PfshY2Xbp6PP6G",
                'open_id': "ou_a17afae1032f5ec0283ca719f4c79311",
                'port': 5557
            }
        }

    # 初始化飞书机器人
    try:
        # 创建并启动长连接机器人（非阻塞模式）
        robot = LarkRobotWS(logger, config)
        robot.start(blocking=False)  # 使用非阻塞模式
        
        # 保持程序运行
        logger.info("飞书机器人已启动，按 Ctrl+C 停止")
        
        # 使用更优雅的方式等待中断
        try:
            # 这种方式比 while True: pass 更友好，不会占用太多 CPU
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("接收到中断信号，正在关闭...")
        
    except Exception as e:
        logger.error(f"飞书机器人运行错误: {e}")
    finally:
        # 确保在任何情况下都尝试停止机器人
        if 'robot' in locals():
            robot.stop()
        logger.info("飞书机器人已关闭")

if __name__ == "__main__":
    main() 