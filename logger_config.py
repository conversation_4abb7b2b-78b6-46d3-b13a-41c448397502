import logging
import os
from datetime import datetime

def setup_logger(name=None, level=logging.INFO, log_to_file=False):
    """
    创建并配置日志记录器
    
    Args:
        name: 日志记录器名称，如果为None则使用root logger
        level: 日志级别，默认为INFO
        log_to_file: 是否同时记录到文件，默认为False
    
    Returns:
        logger: 配置好的日志记录器
    """
    # 获取日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 清除已有的处理器
    if logger.hasHandlers():
        logger.handlers.clear()
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_format = logging.Formatter(
        '%(asctime)s %(name)-8s %(levelname)-8s %(message)s [%(filename)s:%(lineno)d]'
    )
    console_handler.setFormatter(console_format)
    logger.addHandler(console_handler)
    
    # 如果需要，创建文件处理器
    if log_to_file:
        # 确保日志目录存在
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # 创建日志文件名（包含日期）
        today = datetime.now().strftime('%Y%m%d')
        log_filename = f"{log_dir}/{name or 'app'}_{today}.log"
        
        # 配置文件处理器
        file_handler = logging.FileHandler(log_filename, encoding='utf-8')
        file_format = logging.Formatter(
            '%(asctime)s %(name)-8s %(levelname)-8s %(message)s [%(filename)s:%(lineno)d]'
        )
        file_handler.setFormatter(file_format)
        logger.addHandler(file_handler)
    
    return logger 