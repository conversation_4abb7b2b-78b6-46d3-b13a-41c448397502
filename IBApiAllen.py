from queue import Queue
from ibapi.client import EClient
from ibapi.contract import Contract
from ibapi.execution import Execution
from ibapi.order import Order
from ibapi.order_state import OrderState
from ibapi.wrapper import EWrapper
from collections import deque
from ibapi.ticktype import TickTypeEnum
import logging
import threading


class IBApiAllen(EClient, EWrapper):
    def __init__(self, logger: logging.Logger):
        EClient.__init__(self, wrapper=self)
        EWrapper.__init__(self)
        self.data = deque(maxlen=100)  # Store 100 1-minute bars
        self.logger = logger
        self.current_message_id = None  # 用于跟踪当前请求的message_id

        self.ib_helper = None  # 将在setIBHelper中设置

    def setIBHelper(self, ib_helper):
        self.ib_helper = ib_helper

    # EWrapper methods
    def error(self, reqId:int, errorCode:int, errorString:str, advancedOrderRejectJson = ""):
        """Callback for error messages"""
        self.logger.error(f'Error {reqId} {errorCode} {errorString}')

    def connectAck(self):
        """Callback when connection is acknowledged"""
        self.logger.info('Connected')
        if self.ib_helper:
            self.ib_helper.handle_connection()

    def connectionClosed(self):
        """Callback when connection is closed"""
        self.logger.info('Connection closed')
        if self.ib_helper:
            self.ib_helper.handle_connection_closed()


    def currentTime(self, time:int):
        """Server's current time"""
        self.logger.info(f'Current time: {time}')

    def tickPrice(self, reqId:int, tickType:int, price:float, attrib):
        """Market data tick price callback"""
        # self.logger.info(f'Tick price. Req: {reqId}, Type: {TickTypeEnum.idx2name[tickType]}, Price: {price}')
        # 调用IBHelper的处理方法
        if self.ib_helper:
            self.ib_helper.handle_tick_price(reqId, tickType, price, attrib)

    def tickSize(self, reqId:int, tickType:int, size:int):
        """Market data tick size callback"""
        # self.logger.info(f'Tick size. Req: {reqId}, Type: {TickTypeEnum.idx2name[tickType]}, Size: {size}')

    def orderStatus(self, orderId: int, status: str, filled: float,
                   remaining: float, avgFillPrice: float, permId: int,
                   parentId: int, lastFillPrice: float, clientId: int,
                   whyHeld: str, mktCapPrice: float):
        """订单状态回调"""
        self.logger.debug(f'Order Status. Id: {orderId}, Status: {status}, Filled: {filled}, Remaining: {remaining}, AvgFillPrice: {avgFillPrice}, LastFillPrice: {lastFillPrice}')
        if self.ib_helper:
            self.ib_helper.handle_order_status(orderId, status, filled, remaining, avgFillPrice, lastFillPrice)

    def openOrder(self, orderId: int, contract: Contract, order: Order, orderState: OrderState):
        """开放订单回调"""
        self.logger.debug(f'Open Order. Id: {orderId}, Symbol: {contract.symbol}, Action: {order.action}')
        if self.ib_helper:
            self.ib_helper.handle_open_order(orderId, contract, order, orderState)

    def execDetails(self, reqId: int, contract: Contract, execution: Execution):
        """成交回报回调"""
        self.logger.debug(f'Execution. Id: {reqId}, Symbol: {contract.symbol}, Shares: {execution.shares}')
        if self.ib_helper:
            self.ib_helper.handle_execution(reqId, contract, execution)

    def historicalData(self, reqId:int, bar):
        """Historical data callback"""
        self.logger.info(f'Historical data. Req: {reqId}, Time: {bar.date}, Close: {bar.close}')
        self.data.append(bar)

    def historicalDataEnd(self, reqId:int, start:str, end:str):
        """Historical data end callback"""
        self.logger.info(f'Historical data end. Req: {reqId}')

    def nextValidId(self, orderId:int):
        """Next valid order id callback"""
        self.logger.info(f'Next valid order id: {orderId}')
        self.ib_helper.set_next_order_id(orderId)
        
    def contractDetails(self, reqId:int, contractDetails):
        """Contract details callback"""
        self.logger.info(f'Contract details. Req: {reqId}, Symbol: {contractDetails.contract.symbol}')

    def contractDetailsEnd(self, reqId:int):
        """Contract details end callback"""
        self.logger.info(f'Contract details end. Req: {reqId}')

    def marketDataType(self, reqId:int, marketDataType:int):
        """Market data type callback"""
        self.logger.info(f'Market data type. Req: {reqId}, Type: {marketDataType}')

    def realtimeBar(self, reqId:int, time:int, open_:float, high:float,
                    low:float, close:float, volume:int, wap:float, count:int):
        """Real-time bar data callback"""
        self.logger.info(f'Real-time bar. Req: {reqId}, Time: {time}, Close: {close}')

    def position(self, account: str, contract, pos: float, avgCost: float):
        """持仓信息回调"""
        self.logger.info(f'Position. Account: {account}, Symbol: {contract.symbol}, Pos: {pos}, AvgCost: {avgCost}')
        # 调用IBHelper的处理方法
        if self.ib_helper:
            self.ib_helper.handle_position(account, contract, pos, avgCost)

    def positionEnd(self):
        """持仓信息查询结束回调"""
        self.logger.info('Position query completed')
        # 调用IBHelper的处理方法
        if self.ib_helper:
            self.ib_helper.handle_position_end()
            

    def accountSummary(self, reqId:int, account:str, tag:str, value:str, currency:str):
        """Account summary callback"""
        self.logger.info(f'Account summary. Req: {reqId}, Account: {account}, Tag: {tag}, Value: {value}')

    def pnl(self, reqId:int, dailyPnL:float, unrealizedPnL:float, realizedPnL:float):
        """PnL callback"""
        self.logger.info(f'PnL. Req: {reqId}, Daily: {dailyPnL}, Unrealized: {unrealizedPnL}, Realized: {realizedPnL}')

    def pnlSingle(self, reqId:int, pos:int, dailyPnL:float, unrealizedPnL:float, realizedPnL:float, value:float):
        """Single position PnL callback"""
        self.logger.info(f'Single PnL. Req: {reqId}, Pos: {pos}, Daily: {dailyPnL}')

    def managedAccounts(self, accountsList:str):
        """Managed accounts callback"""
        self.logger.info(f'Managed accounts: {accountsList}')

    def updateAccountValue(self, key:str, val:str, currency:str, accountName:str):
        """Account value update callback"""
        self.logger.info(f'Account update. Account: {accountName}, Key: {key}, Value: {val}')

    def updatePortfolio(self, contract, position:float, marketPrice:float,
                       marketValue:float, averageCost:float, unrealizedPNL:float,
                       realizedPNL:float, accountName:str):
        """Portfolio update callback"""
        self.logger.info(f'Portfolio update. Account: {accountName}, Symbol: {contract.symbol}, Position: {position}')

    def historicalTicksBidAsk(self, reqId:int, ticks, done:bool):
        """Historical ticks bid/ask callback"""
        self.logger.info(f"Historical ticks bid/ask. Req: {reqId}, Done: {done}")

    def historicalTicksLast(self, reqId:int, ticks, done:bool):
        """Historical ticks last callback"""
        self.logger.info(f"Historical ticks last. Req: {reqId}, Done: {done}")

    def tickByTickAllLast(self, reqId:int, tickType:int, time:int, price:float,
                         size:int, tickAttribLast, exchange:str, specialConditions:str):
        """Tick by tick all last callback"""
        self.logger.info(f"Tick by tick all last. Req: {reqId}, tickType: {TickTypeEnum.idx2name[tickType]}, Price: {price}, Size: {size}")

    def tickByTickBidAsk(self, reqId:int, time:int, bidPrice:float, askPrice:float,
                         bidSize:int, askSize:int, tickAttribBidAsk):
        """Tick by tick bid/ask callback"""
        self.logger.info(f"Tick by tick bid/ask. Req: {reqId}, Bid: {bidPrice}, Ask: {askPrice}")

    def tickByTickMidPoint(self, reqId:int, time:int, midPoint:float):
        """Tick by tick midpoint callback"""
        self.logger.info(f"Tick by tick midpoint. Req: {reqId}, MidPoint: {midPoint}")

    def orderBound(self, reqId:int, apiClientId:int, apiOrderId:int):
        """Order bound callback"""
        self.logger.info(f"Order bound. Req: {reqId}, API Client ID: {apiClientId}, Order ID: {apiOrderId}")

    def completedOrder(self, contract, order, orderState):
        """Completed order callback"""
        self.logger.info(f"Completed order. Symbol: {contract.symbol}, Status: {orderState.status}")

    def completedOrdersEnd(self):
        """Completed orders end callback"""
        self.logger.info("Completed orders end")

    def historicalSchedule(self, reqId:int, startDateTime:str, endDateTime:str,
                          timeZone:str, sessions):
        """Historical schedule callback"""
        self.logger.info(f"Historical schedule. Req: {reqId}, TimeZone: {timeZone}")

    def userInfo(self, reqId:int, whiteBrandingId:str):
        """User info callback"""
        self.logger.info(f"User info. Req: {reqId}, Branding ID: {whiteBrandingId}")

    def tickGeneric(self, reqId:int, tickType:int, value:float):
        """Generic tick callback"""
        # self.logger.info(f"Tick generic. Req: {reqId}, Type: {TickTypeEnum.idx2name[tickType]}, Value: {value}")

    def tickString(self, reqId:int, tickType:int, value:str):
        """String tick callback"""
        # self.logger.info(f"Tick string. Req: {reqId}, Type: {TickTypeEnum.idx2name[tickType]}, Value: {value}")

    def tickEFP(self, reqId:int, tickType:int, basisPoints:float,
                formattedBasisPoints:str, totalDividends:float,
                holdDays:int, futureLastTradeDate:str, dividendImpact:float,
                dividendsToLastTradeDate:float):
        """EFP tick callback"""
        self.logger.info(f"Tick EFP. Req: {reqId}, Basis Points: {basisPoints}")

    def updateAccountTime(self, timeStamp:str):
        """Account time update callback"""
        self.logger.info(f"Account time update: {timeStamp}")

    def accountDownloadEnd(self, accountName:str):
        """Account download end callback"""
        self.logger.info(f"Account download end: {accountName}")

    def bondContractDetails(self, reqId:int, contractDetails):
        """Bond contract details callback"""
        self.logger.info(f"Bond contract details. Req: {reqId}")

    def execDetailsEnd(self, reqId:int):
        """Execution details end callback"""
        self.logger.info(f"Execution details end. Req: {reqId}")

    def updateMktDepth(self, reqId:int, position:int, operation:int,
                      side:int, price:float, size:int):
        """Market depth update callback"""
        self.logger.info(f"Market depth update. Req: {reqId}, Position: {position}, Price: {price}")

    def updateMktDepthL2(self, reqId:int, position:int, marketMaker:str,
                        operation:int, side:int, price:float, size:int, isSmartDepth:bool):
        """Market depth L2 update callback"""
        self.logger.info(f"Market depth L2 update. Req: {reqId}, Position: {position}, Price: {price}")

    def updateNewsBulletin(self, msgId:int, msgType:int, newsMessage:str, originExch:str):
        """News bulletin update callback"""
        self.logger.info(f"News bulletin. ID: {msgId}, Message: {newsMessage}")

    def receiveFA(self, faDataType:int, xml:str):
        """FA information callback"""
        self.logger.info(f"Received FA data. Type: {faDataType}")

    def verifyMessageAPI(self, apiData:str):
        """API verification message callback"""
        self.logger.info(f"API verification message")

    def verifyCompleted(self, isSuccessful:bool, errorText:str):
        """Verification completed callback"""
        self.logger.info(f"Verification completed. Success: {isSuccessful}")

    def verifyAndAuthMessageAPI(self, apiData:str, xyzChallenge:str):
        """API verification and authentication message callback"""
        self.logger.info(f"API verification and authentication message")

    def verifyAndAuthCompleted(self, isSuccessful:bool, errorText:str):
        """Verification and authentication completed callback"""
        self.logger.info(f"Verification and authentication completed. Success: {isSuccessful}")

    def displayGroupList(self, reqId:int, groups:str):
        """Display group list callback"""
        self.logger.info(f"Display group list. Req: {reqId}, Groups: {groups}")

    def displayGroupUpdated(self, reqId:int, contractInfo:str):
        """Display group updated callback"""
        self.logger.info(f"Display group updated. Req: {reqId}, Info: {contractInfo}")

    def positionMultiEnd(self, reqId:int):
        """Position multi end callback"""
        self.logger.info(f"Position multi end. Req: {reqId}")

    def accountUpdateMultiEnd(self, reqId:int):
        """Account update multi end callback"""
        self.logger.info(f"Account update multi end. Req: {reqId}")

    def securityDefinitionOptionParameter(self, reqId:int, exchange:str,
                                        underlyingConId:int, tradingClass:str,
                                        multiplier:str, expirations, strikes):
        """Security definition option parameter callback"""
        self.logger.info(f"Security definition option parameter. Req: {reqId}, Exchange: {exchange}")

    def securityDefinitionOptionParameterEnd(self, reqId:int):
        """Security definition option parameter end callback"""
        self.logger.info(f"Security definition option parameter end. Req: {reqId}")

    def softDollarTiers(self, reqId:int, tiers):
        """Soft dollar tiers callback"""
        self.logger.info(f"Soft dollar tiers. Req: {reqId}")

    def familyCodes(self, familyCodes):
        """Family codes callback"""
        self.logger.info("Family codes received")

    def symbolSamples(self, reqId:int, contractDescriptions):
        """Symbol samples callback"""
        self.logger.info(f"Symbol samples. Req: {reqId}")

    def mktDepthExchanges(self, depthMktDataDescriptions):
        """Market depth exchanges callback"""
        self.logger.info("Market depth exchanges received")

    def tickNews(self, tickerId:int, timeStamp:int, providerCode:str,
                articleId:str, headline:str, extraData:str):
        """News tick callback"""
        self.logger.info(f"News tick. ID: {tickerId}, Headline: {headline}")

    def smartComponents(self, reqId:int, smartComponentMap):
        """Smart components callback"""
        self.logger.info(f"Smart components. Req: {reqId}")

    def tickReqParams(self, tickerId: int, minTick: float, bboExchange: str, snapshotPermissions: int):
        """Tick request parameters callback"""
        self.logger.debug(f"Tick request parameters. ID: {tickerId}, MinTick: {minTick}, Exchange: {bboExchange}")
        # 调用IBHelper的处理方法
        if self.ib_helper:
            self.ib_helper.handle_tick_req_params(tickerId, minTick, bboExchange, snapshotPermissions)

    def newsProviders(self, newsProviders):
        """News providers callback"""
        self.logger.info("News providers received")

    def newsArticle(self, requestId:int, articleType:int, articleText:str):
        """News article callback"""
        self.logger.info(f"News article. Req: {requestId}, Type: {articleType}")

    def historicalNews(self, requestId:int, time:str, providerCode:str,
                      articleId:str, headline:str):
        """Historical news callback"""
        self.logger.info(f"Historical news. Req: {requestId}, Headline: {headline}")

    def historicalNewsEnd(self, requestId:int, hasMore:bool):
        """Historical news end callback"""
        self.logger.info(f"Historical news end. Req: {requestId}, Has more: {hasMore}")

    def headTimestamp(self, reqId:int, headTimestamp:str):
        """Head timestamp callback"""
        self.logger.info(f"Head timestamp. Req: {reqId}, Timestamp: {headTimestamp}")

    def histogramData(self, reqId:int, items):
        """Histogram data callback"""
        self.logger.info(f"Histogram data. Req: {reqId}")

    def historicalDataUpdate(self, reqId:int, bar):
        """Historical data update callback"""
        self.logger.info(f"Historical data update. Req: {reqId}, Time: {bar.date}, Close: {bar.close}")

    def rerouteMktDataReq(self, reqId:int, conId:int, exchange:str):
        """Reroute market data request callback"""
        self.logger.info(f"Reroute market data request. Req: {reqId}, Exchange: {exchange}")

    def rerouteMktDepthReq(self, reqId:int, conId:int, exchange:str):
        """Reroute market depth request callback"""
        self.logger.info(f"Reroute market depth request. Req: {reqId}, Exchange: {exchange}")

    def marketRule(self, marketRuleId:int, priceIncrements):
        """Market rule callback"""
        self.logger.info(f"Market rule. ID: {marketRuleId}")

    def wshMetaData(self, reqId:int, dataJson:str):
        """WSH metadata callback"""
        self.logger.info(f"WSH metadata. Req: {reqId}")

    def wshEventData(self, reqId:int, dataJson:str):
        """WSH event data callback"""
        self.logger.info(f"WSH event data. Req: {reqId}")

    def historicalSchedule(self, reqId:int, startDateTime:str, endDateTime:str,
                         timeZone:str, sessions):
        """Historical schedule callback"""
        self.logger.info(f"Historical schedule. Req: {reqId}, TimeZone: {timeZone}")

    def userInfo(self, reqId:int, whiteBrandingId:str):
        """User info callback"""
        self.logger.info(f"User info. Req: {reqId}, Branding ID: {whiteBrandingId}")

    def commissionReport(self, commissionReport):
        """佣金报告回调"""
        self.logger.debug(f'Commission Report. ExecId: {commissionReport.execId}, Commission: {commissionReport.commission}')
        if self.ib_helper:
            self.ib_helper.handle_commission_report(commissionReport)

    # You can also override EClient methods if needed
    def connect(self, host:str, port:int, clientId:int):
        """Connect to TWS/IB Gateway"""
        self.logger.info(f'Connecting to {host}:{port} with client ID {clientId}')
        super().connect(host, port, clientId)

    def disconnect(self):
        """Disconnect from TWS/IB Gateway"""
        self.logger.info('Disconnecting')
        super().disconnect()

    def reqMktData(self, reqId:int, contract, genericTickList:str,
                   snapshot:bool, regulatorySnapshot:bool, mktDataOptions):
        """Request market data"""
        self.logger.info(f'Requesting market data for {contract.symbol}')
        super().reqMktData(reqId, contract, genericTickList,
                          snapshot, regulatorySnapshot, mktDataOptions)

    def get_historical_data(self):
        """Get stored historical data"""
        return list(self.data)

    def reqCurrentTime(self):
        """Requests current server time"""
        self.logger.info("Requesting current time")
        super().reqCurrentTime()

    def serverVersion(self):
        """Returns TWS/IBGW version"""
        version = super().serverVersion()
        # self.logger.info(f"Server version: {version}")
        return version

    def setServerLogLevel(self, logLevel:int):
        """Sets the server logging level"""
        self.logger.info(f"Setting server log level to {logLevel}")
        super().setServerLogLevel(logLevel)

    def reqMktDepth(self, reqId:int, contract, numRows:int,
                    isSmartDepth:bool, mktDepthOptions):
        """Request market depth"""
        self.logger.info(f"Requesting market depth for {contract.symbol}")
        super().reqMktDepth(reqId, contract, numRows, isSmartDepth, mktDepthOptions)

    def cancelMktDepth(self, reqId:int, isSmartDepth:bool):
        """Cancel market depth request"""
        self.logger.info(f"Canceling market depth request {reqId}")
        super().cancelMktDepth(reqId, isSmartDepth)

    def reqHistoricalData(self, reqId:int, contract, endDateTime:str,
                         durationStr:str, barSizeSetting:str, whatToShow:str,
                         useRTH:int, formatDate:int, keepUpToDate:bool, chartOptions):
        """Request historical data"""
        self.logger.info(f"Requesting historical data for {contract.symbol}")
        super().reqHistoricalData(reqId, contract, endDateTime, durationStr,
                                barSizeSetting, whatToShow, useRTH, formatDate,
                                keepUpToDate, chartOptions)

    def reqContractDetails(self, reqId:int, contract):
        """Request contract details"""
        self.logger.info(f"Requesting contract details for {contract.symbol}")
        super().reqContractDetails(reqId, contract)

    def reqIds(self, numIds:int):
        """Request the next valid order ID"""
        self.logger.info(f"Requesting {numIds} order IDs")
        super().reqIds(numIds)

    def placeOrder(self, orderId:int, contract, order):
        """Place a new order"""
        self.logger.info(f"Placing order {orderId} for {contract.symbol}")
        super().placeOrder(orderId, contract, order)

    def cancelOrder(self, orderId:int, manualCancelOrderTime:str = ""):
        """Cancel an order"""
        self.logger.info(f"Canceling order {orderId}")
        super().cancelOrder(orderId, manualCancelOrderTime)

    def reqAccountUpdates(self, subscribe:bool, acctCode:str):
        """Request account updates"""
        self.logger.info(f"Requesting account updates for {acctCode}")
        super().reqAccountUpdates(subscribe, acctCode)

    def reqOpenOrders(self):
        """Request all open orders"""
        self.logger.info("Requesting open orders")
        super().reqOpenOrders()

    def reqExecutions(self, reqId:int, filter):
        """Request executions"""
        self.logger.info(f"Requesting executions for reqId {reqId}")
        super().reqExecutions(reqId, filter)

    def reqPositions(self):
        """Request positions"""
        self.logger.info("Requesting positions")
        super().reqPositions()

    def cancelPositions(self):
        """Cancel positions request"""
        self.logger.info("Canceling positions request")
        super().cancelPositions()

    def reqAccountSummary(self, reqId:int, groupName:str, tags:str):
        """Request account summary"""
        self.logger.info(f"Requesting account summary for group {groupName}")
        super().reqAccountSummary(reqId, groupName, tags)

    def cancelAccountSummary(self, reqId:int):
        """Cancel account summary request"""
        self.logger.info(f"Canceling account summary request {reqId}")
        super().cancelAccountSummary(reqId)

    def reqPnL(self, reqId:int, account:str, modelCode:str):
        """Request PnL updates"""
        self.logger.info(f"Requesting PnL for account {account}")
        super().reqPnL(reqId, account, modelCode)

    def cancelPnL(self, reqId:int):
        """Cancel PnL updates"""
        self.logger.info(f"Canceling PnL request {reqId}")
        super().cancelPnL(reqId)

    def reqPnLSingle(self, reqId:int, account:str, modelCode:str, conId:int):
        """Request PnL updates for a single position"""
        self.logger.info(f"Requesting PnL for account {account} and contract {conId}")
        super().reqPnLSingle(reqId, account, modelCode, conId)

    def cancelPnLSingle(self, reqId:int):
        """Cancel single position PnL updates"""
        self.logger.info(f"Canceling single PnL request {reqId}")
        super().cancelPnLSingle(reqId)

    def reqScannerParameters(self):
        """Request scanner parameters"""
        self.logger.info("Requesting scanner parameters")
        super().reqScannerParameters()

    def reqScannerSubscription(self, reqId:int, subscription, scannerSubscriptionOptions,
                              scannerSubscriptionFilterOptions):
        """Request scanner subscription"""
        self.logger.info(f"Requesting scanner subscription {reqId}")
        super().reqScannerSubscription(reqId, subscription,
                                     scannerSubscriptionOptions,
                                     scannerSubscriptionFilterOptions)

    def cancelScannerSubscription(self, reqId:int):
        """Cancel scanner subscription"""
        self.logger.info(f"Canceling scanner subscription {reqId}")
        super().cancelScannerSubscription(reqId)

    def reqRealTimeBars(self, reqId:int, contract, barSize:int,
                        whatToShow:str, useRTH:bool, realTimeBarsOptions):
        """Request real-time bars"""
        self.logger.info(f"Requesting real-time bars for {contract.symbol}")
        super().reqRealTimeBars(reqId, contract, barSize, whatToShow,
                               useRTH, realTimeBarsOptions)

    def cancelRealTimeBars(self, reqId:int):
        """Cancel real-time bars"""
        self.logger.info(f"Canceling real-time bars request {reqId}")
        super().cancelRealTimeBars(reqId)

    def reqFundamentalData(self, reqId:int, contract, reportType:str,
                          fundamentalDataOptions):
        """Request fundamental data"""
        self.logger.info(f"Requesting fundamental data for {contract.symbol}")
        super().reqFundamentalData(reqId, contract, reportType,
                                  fundamentalDataOptions)

    def cancelFundamentalData(self, reqId:int):
        """Cancel fundamental data request"""
        self.logger.info(f"Canceling fundamental data request {reqId}")
        super().cancelFundamentalData(reqId)

    def calculateImpliedVolatility(self, reqId:int, contract,
                                  optionPrice:float, underPrice:float,
                                  implVolOptions):
        """Calculate implied volatility"""
        self.logger.info(f"Calculating implied volatility for {contract.symbol}")
        super().calculateImpliedVolatility(reqId, contract, optionPrice,
                                         underPrice, implVolOptions)

    def calculateOptionPrice(self, reqId:int, contract,
                           volatility:float, underPrice:float,
                           optPrcOptions):
        """Calculate option price"""
        self.logger.info(f"Calculating option price for {contract.symbol}")
        super().calculateOptionPrice(reqId, contract, volatility,
                                   underPrice, optPrcOptions)

    def cancelCalculateImpliedVolatility(self, reqId:int):
        """Cancel implied volatility calculation"""
        self.logger.info(f"Canceling implied volatility calculation {reqId}")
        super().cancelCalculateImpliedVolatility(reqId)

    def cancelCalculateOptionPrice(self, reqId:int):
        """Cancel option price calculation"""
        self.logger.info(f"Canceling option price calculation {reqId}")
        super().cancelCalculateOptionPrice(reqId)

    def reqGlobalCancel(self):
        """Cancel all orders globally"""
        self.logger.info("Requesting global cancel")
        super().reqGlobalCancel()

    def reqMarketDataType(self, marketDataType:int):
        """Request market data type"""
        self.logger.info(f"Setting market data type to {marketDataType}")
        super().reqMarketDataType(marketDataType)

    def reqPositionsMulti(self, reqId:int, account:str, modelCode:str):
        """Request positions for multiple accounts"""
        self.logger.info(f"Requesting positions for account {account}")
        super().reqPositionsMulti(reqId, account, modelCode)

    def cancelPositionsMulti(self, reqId:int):
        """Cancel positions request for multiple accounts"""
        self.logger.info(f"Canceling positions request {reqId}")
        super().cancelPositionsMulti(reqId)

    def reqAccountUpdatesMulti(self, reqId:int, account:str, modelCode:str,
                              ledgerAndNLV:bool):
        """Request account updates for multiple accounts"""
        self.logger.info(f"Requesting account updates for {account}")
        super().reqAccountUpdatesMulti(reqId, account, modelCode, ledgerAndNLV)

    def cancelAccountUpdatesMulti(self, reqId:int):
        """Cancel account updates for multiple accounts"""
        self.logger.info(f"Canceling account updates request {reqId}")
        super().cancelAccountUpdatesMulti(reqId)


