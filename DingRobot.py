# !/usr/bin/env python

import logging
from logger_config import setup_logger
from dingtalk_stream import AckMessage, ChatbotMessage, ChatbotHandler, CallbackMessage
import threading
import zmq
import json
from DingWebhook import DingWebhook


class DingRobotHandler(ChatbotHandler):
    def __init__(self, config=None):
        super().__init__()
        # 创建自己的日志器
        self.logger = setup_logger("DingRobot")
        self.config = config['dingtalk']
        self.port = self.config['port']
        self.topic = self.config['topic']  # 获取配置的topic
        self.saved_original_message = None
        
        # 设置ZMQ
        self.context = zmq.Context()
        
        # 命令发布器 (PUB)
        self.command_publisher = self.context.socket(zmq.PUB)
        self.command_publisher.connect(f"tcp://localhost:{self.port}")
        
        # 消息接收器 (SUB)
        self.message_subscriber = self.context.socket(zmq.SUB)
        self.message_subscriber.connect("tcp://localhost:5555")
        # 订阅主题：topic 和 空字符串(用于接收所有消息)
        self.message_subscriber.setsockopt_string(zmq.SUBSCRIBE, self.topic)
        self.message_subscriber.setsockopt_string(zmq.SUBSCRIBE, "")
        
        # 存储消息ID和原始消息的映射
        self.message_map = {}
        self.message_map_lock = threading.Lock() 
        
        # 启动消息处理线程
        self.message_handler = threading.Thread(target=self.handle_messages)
        self.message_handler.daemon = True
        self.message_handler.start()

        # 初始化钉钉webhook时使用自己的logger
        webhook_config = self.config['webhook']
        self.ding_webhook = DingWebhook(
            webhook_url=webhook_config['url'],
            secret=webhook_config['secret'],
            logger=self.logger  # 传递自己的logger
        )

    async def process(self, callback: CallbackMessage):
        """处理接收到的消息"""
        try:
            message = ChatbotMessage.from_dict(callback.data)
            text = message.text.content.strip()
            self.saved_original_message = message
            
            # 存储消息映射
            with self.message_map_lock:
                self.message_map[message.message_id] = message
            
            # 通过ZMQ发送命令，带上topic前缀
            self.command_publisher.send_string(f"{self.topic}+++{text}")
            
            # 发送确认信息并包含指令原文
            self.reply_text(f"已收到指令: {text}，正在处理...", message)
            
            return AckMessage.STATUS_OK, 'OK'
            
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            return AckMessage.STATUS_ERROR, str(e)

    def handle_messages(self):
        """处理消息队列中的消息"""
        while True:
            try:
                # 从ZMQ接收消息
                message = self.message_subscriber.recv_string()
                self.process_message(message)
            except Exception as e:
                self.logger.error(f"处理消息失败: {e}")

    def process_message(self, message: str):
        """处理消息并发送到钉钉"""
        try:
            # 检查消息是否包含topic
            content = message
            if "+++" in message:
                topic, content = message.split("+++", 1)
                # 如果topic不是当前机器人的topic且不是空字符串，则忽略
                if topic != self.topic and topic != "":
                    return
            
            # 检查消息是否有HOOK头
            if content.startswith("HOOK"):
                # 移除HOOK头并发送消息
                actual_message = content[4:].lstrip()  # 移除"HOOK"和左侧所有空格
                if self.ding_webhook:
                    self.ding_webhook.send_message(actual_message)  # 发送处理后的消息
                else:
                    self.logger.warning(f"Webhook未初始化，无法发送消息：{actual_message}")
                return

            # 原有的消息处理逻辑
            with self.message_map_lock:
                if self.message_map:
                    original_message = list(self.message_map.values())[-1]
                    self.message_map.clear()  # 清理消息映射
                else:
                    original_message = None
            
            # 如果message_map为空，使用保存的original_message
            if not original_message:
                original_message = self.saved_original_message
            
            if original_message:
                # 发送响应
                self.reply_text(content, original_message)
            else:
                # 使用webhook发送消息
                if self.ding_webhook:
                    self.ding_webhook.send_message(content)
                else:
                    self.logger.warning(f"没有找到对应的原始消息，且Webhook未初始化，响应内容：{content}")
            
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")

    def __del__(self):
        """清理ZMQ资源"""
        self.command_publisher.close()
        self.message_subscriber.close()
        self.context.term()