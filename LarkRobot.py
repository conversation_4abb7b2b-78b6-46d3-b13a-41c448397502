from logger_config import setup_logger
import logging
import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from lark_oapi.core.enum import LogLevel
import threading
import zmq
import time
import json

class LarkRobot:
    """
    飞书机器人长连接类，用于接收和处理消息
    """
    
    def __init__(self, config: dict):
        """
        初始化飞书机器人长连接
        
        Args:
            config: 完整的配置字典
        """
        # 创建自己的日志器
        self.logger = setup_logger("LarkRobot")
        self.config = config['lark']
        self.app_id = self.config['app_id']
        self.app_secret = self.config['app_secret']
        self.open_id = self.config['open_id']
        self.port = self.config['port']
        self.topic = self.config['topic']  # 获取topic配置
        
        # 创建客户端
        self.client = lark.Client.builder() \
            .app_id(self.app_id) \
            .app_secret(self.app_secret) \
            .build()
        
        # 简化事件处理器注册
        self.event_handler = lark.EventDispatcherHandler.builder("", "") \
            .register_p2_im_message_receive_v1(self._handle_message) \
            .build()
        
        # 确保 WebSocket 客户端正确配置
        self.ws_client = lark.ws.Client(
            self.app_id,
            self.app_secret,
            event_handler=self.event_handler,
            log_level=LogLevel.DEBUG,
        )
        
        self.running = False
        
        # 创建 ZMQ 上下文和套接字
        self.context = zmq.Context()
        self.command_publisher = self.context.socket(zmq.PUB)
        self.command_publisher.connect(f"tcp://127.0.0.1:{self.port}")
        
        self.message_subscriber = self.context.socket(zmq.SUB)
        self.message_subscriber.connect("tcp://127.0.0.1:5555")
        # 订阅自己的topic和空字符串(用于接收所有消息)
        self.message_subscriber.setsockopt_string(zmq.SUBSCRIBE, self.topic)
        self.message_subscriber.setsockopt_string(zmq.SUBSCRIBE, "")
        
        # 存储消息ID和原始消息的映射
        self.message_map = {}
        self.message_map_lock = threading.Lock()
        
        # 启动消息处理线程
        self.message_handler_thread = threading.Thread(target=self.handle_messages)
        self.message_handler_thread.daemon = True
        self.message_handler_thread.start()
        
        self.logger.info("ZMQ 已初始化")
    
    def _handle_message(self, data: P2ImMessageReceiveV1) -> None:
        """
        处理接收到的消息
        
        Args:
            data: 接收到的消息数据
        """
        try:
            message = data.event.message
            msg_type = message.message_type
            content = {}
            
            self.logger.info(f"收到原始消息: {message}")
            
            # 解析不同类型的消息内容
            if msg_type == "text":
                content = json.loads(message.content)
                text = content.get("text", "")
                self.logger.info(f"收到文本消息: {text}")
                
                # 存储消息映射
                with self.message_map_lock:
                    self.message_map[message.message_id] = message
                
                # 通过命令发布器发送命令，添加topic前缀
                self.command_publisher.send_string(f"{self.topic}+++{text}")
                
                # 回复用户，确认收到消息
                self._reply_message(message.message_id, "text", {"text": f"已收到您的消息: {text}，正在处理..."})
            
            elif msg_type == "post":
                content = json.loads(message.content)
                self.logger.info(f"收到富文本消息: {content}")
                
                # 存储消息映射
                with self.message_map_lock:
                    self.message_map[message.message_id] = message
                
                # 处理富文本消息 (可根据需要添加)
                self._reply_message(message.message_id, "text", {"text": "已收到您的富文本消息"})
            
            else:
                self.logger.info(f"收到其他类型消息: {msg_type}")
                
                # 存储消息映射
                with self.message_map_lock:
                    self.message_map[message.message_id] = message
                
                # 回复消息
                self._reply_message(message.message_id, "text", {"text": f"已收到您的{msg_type}类型消息"})
        
        except Exception as e:
            self.logger.error(f"处理消息时发生异常: {str(e)}", exc_info=True)
    
    def handle_commands(self):
        """处理从ZMQ接收到的命令"""
        self.logger.info("命令处理线程已启动")
        while True:
            try:
                # 从ZMQ接收命令
                command = self.command_subscriber.recv_string()
                self.logger.info(f"收到命令: {command}")
                self.process_command(command)
            except Exception as e:
                self.logger.error(f"处理命令时发生异常: {str(e)}", exc_info=True)
    
    def process_command(self, command: str):
        """
        处理命令
        
        Args:
            command: 接收到的命令
        """
        try:
            self.logger.info(f"处理命令: {command}")
            
            # 这里不直接回复用户，而是将命令通过 ZMQ 发送给执行者
            # 执行者处理完后，会通过 ZMQ 发送结果，然后再回复用户
            
            # 如果需要确认命令已收到，可以发送一个简短的确认消息
            with self.message_map_lock:
                if self.message_map:
                    message_id = list(self.message_map.keys())[-1]
                    self._reply_message(message_id, "text", {"text": "命令已接收，正在处理..."})
                    # 不清理消息映射，保留以便后续回复
                else:
                    self.command_publisher.send_string(command)
        
        except Exception as e:
            self.logger.error(f"处理命令失败: {e}", exc_info=True)
    
    def send_to_user(self, message: str):
        """
        发送消息给用户
        
        Args:
            message: 要发送的消息
        """
        try:
            # 使用 open_id 发送消息
            request = CreateMessageRequest.builder() \
                .receive_id_type("open_id") \
                .request_body(
                    CreateMessageRequestBody.builder()
                    .receive_id(self.open_id)
                    .msg_type("text")
                    .content(json.dumps({"text": message}))
                    .build()
                ) \
                .build()
            
            response = self.client.im.v1.message.create(request)
            
            if response.success():
                self.logger.info(f"发送消息成功: {response.data.message_id}")
            else:
                self.logger.error(f"发送消息失败: {response.code}, {response.msg}, log_id: {response.get_log_id()}")
        
        except Exception as e:
            self.logger.error(f"发送消息时发生异常: {str(e)}", exc_info=True)
    
    def _reply_message(self, message_id: str, msg_type: str, content: Dict) -> bool:
        """
        回复消息
        
        Args:
            message_id: 要回复的消息ID
            msg_type: 消息类型
            content: 消息内容
            
        Returns:
            bool: 是否发送成功
        """
        try:
            request = ReplyMessageRequest.builder() \
                .message_id(message_id) \
                .request_body(
                    ReplyMessageRequestBody.builder()
                    .content(json.dumps(content))
                    .msg_type(msg_type)
                    .build()
                ) \
                .build()
            
            response = self.client.im.v1.message.reply(request)
            
            if response.success():
                self.logger.info(f"回复消息成功: {response.data.message_id}")
                return True
            else:
                self.logger.error(f"回复消息失败: {response.code}, {response.msg}, log_id: {response.get_log_id()}")
                return False
        
        except Exception as e:
            self.logger.error(f"回复消息时发生异常: {str(e)}")
            return False
    
    def send_message(self, chat_id: str, msg_type: str, content: Dict) -> bool:
        """
        发送消息到指定聊天
        
        Args:
            chat_id: 聊天ID
            msg_type: 消息类型
            content: 消息内容
            
        Returns:
            bool: 是否发送成功
        """
        try:
            request = CreateMessageRequest.builder() \
                .receive_id_type("chat_id") \
                .request_body(
                    CreateMessageRequestBody.builder()
                    .receive_id(chat_id)
                    .msg_type(msg_type)
                    .content(json.dumps(content))
                    .build()
                ) \
                .build()
            
            response = self.client.im.v1.message.create(request)
            
            if response.success():
                self.logger.info(f"发送消息成功: {response.data.message_id}")
                return True
            else:
                self.logger.error(f"发送消息失败: {response.code}, {response.msg}, log_id: {response.get_log_id()}")
                return False
        
        except Exception as e:
            self.logger.error(f"发送消息时发生异常: {str(e)}")
            return False
    
    def start(self, blocking=False):
        """
        启动长连接
        
        Args:
            blocking: 是否阻塞当前线程，默认为 False
        """
        
        if blocking:
            # 阻塞模式直接启动
            self.ws_client.start()
        else:
            # 非阻塞模式使用线程启动
            self.ws_thread = threading.Thread(target=self.ws_client.start)
            self.ws_thread.daemon = True  # 设置为守护线程，主线程结束时自动结束
            self.ws_thread.start()
            self.logger.info("飞书机器人已启动")
    
    def stop(self):
        """
        停止长连接
        """
        self.logger.info("停止飞书机器人...")
        try:
            # 设置停止标志
            self.running = False
            
            # 关闭 ZMQ 相关资源
            if hasattr(self, 'message_subscriber'):
                # 使用 linger=0 确保立即关闭，不等待未发送的消息
                self.message_subscriber.close(linger=0)
                
            if hasattr(self, 'message_publisher'):
                self.message_publisher.close(linger=0)
                
            if hasattr(self, 'zmq_context'):
                # 等待所有线程完成
                time.sleep(0.5)  # 给线程一些时间退出
                self.zmq_context.term()
            
            # 等待消息处理线程结束
            if hasattr(self, 'message_thread') and self.message_thread.is_alive():
                self.message_thread.join(timeout=2.0)  # 最多等待2秒
                
            # 最后关闭 WebSocket 连接
            if hasattr(self, 'ws_client'):
                self.ws_client.disconnect()
                
        except Exception as e:
            self.logger.error(f"停止飞书机器人时发生异常: {str(e)}")

    def handle_messages(self):
        """处理从ZMQ接收到的消息"""
        self.logger.info("消息处理线程已启动")
        while True:
            try:
                # 从ZMQ接收消息
                message = self.message_subscriber.recv_string()
                self.logger.info(f"收到消息: {message}")
                self.process_message(message)
            except Exception as e:
                self.logger.error(f"处理消息时发生异常: {str(e)}", exc_info=True)

    def process_message(self, message: str):
        """
        处理消息
        
        Args:
            message: 接收到的消息
        """
        try:
            # 检查消息是否包含topic
            content = message
            if "+++" in message:
                topic, content = message.split("+++", 1)
                # 如果topic不是当前机器人的topic且不是空字符串，则忽略
                if topic != self.topic and topic != "":
                    return
            
            # 检查消息是否有HOOK头，如果有则去掉
            if content.startswith("HOOK"):
                content = content[4:].lstrip()  # 移除"HOOK"和左侧所有空格
            
            # 获取最近的消息ID进行回复
            with self.message_map_lock:
                if self.message_map:
                    message_id = list(self.message_map.keys())[-1]
                    self._reply_message(message_id, "text", {"text": content})
                    # 回复后可以清理消息映射
                    self.message_map.clear()
                else:
                    # 没有最近的消息，直接发送给用户
                    self.send_to_user(content)
        
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}", exc_info=True)
