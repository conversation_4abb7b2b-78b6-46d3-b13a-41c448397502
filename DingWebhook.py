import requests
import time
import hmac
import hashlib
import base64
import urllib.parse
import logging
from typing import Optional
from datetime import datetime

class DingWebhook:
    def __init__(self, webhook_url: str, secret: str, logger: Optional[logging.Logger] = None):
        """
        初始化钉钉webhook
        Args:
            webhook_url: 钉钉webhook地址
            secret: 安全设置的签名密钥
            logger: 日志记录器
        """
        self.webhook_url = webhook_url
        self.secret = secret
        self.logger = logger or logging.getLogger(__name__)

    def _generate_signature(self) -> str:
        """
        生成签名
        Returns:
            签名字符串
        """
        timestamp = str(round(time.time() * 1000))
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return timestamp, sign

    def send_message(self, content: str) -> bool:
        """
        发送消息
        Args:
            content: 消息内容
        Returns:
            是否发送成功
        """
        try:
            timestamp, sign = self._generate_signature()
            url = f"{self.webhook_url}&timestamp={timestamp}&sign={sign}"
            
            headers = {'Content-Type': 'application/json'}
            data = {
                "msgtype": "text",
                "text": {
                    "content": content
                }
            }
            
            response = requests.post(url, headers=headers, json=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.logger.info(f"消息发送成功: {content}")
                    return True
                else:
                    self.logger.error(f"消息发送失败: {result}")
            else:
                self.logger.error(f"请求失败: {response.status_code}")
            
            return False
            
        except Exception as e:
            self.logger.error(f"发送消息时发生错误: {e}")
            return False

