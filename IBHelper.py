import argparse
import logging
import queue
from typing import Optional
import threading
import time
from logger_config import setup_logger
from IBApiAllen import IBApiAllen
from ibapi.contract import Contract
import os
import json
from dataclasses import dataclass

from ibapi.execution import Execution
from ibapi.order import Order
from ibapi.order_state import OrderState
import zmq
from datetime import datetime, timedelta
import pytz

@dataclass
class Task:
    """任务数据类"""
    command: str          # 指令
    params: dict         # 参数
    message_id: str      # 消息ID，用于关联响应
    timestamp: float     # 时间戳
    
@dataclass
class Result:
    """结果数据类"""
    message_id: str      # 对应的消息ID
    content: str         # 响应内容
    status: bool         # 执行状态
    error: Optional[str] = None  # 错误信息

class IBHelper:
    def __init__(self, config=None):
        # 创建自己的日志器
        self.logger = setup_logger("IBHelper")
        self.app = IBApiAllen(self.logger)
        self.app.setIBHelper(self)
        self.reqId = 0
        self.contracts = {}
        
        # 设置机器人类型和配置
        self.config = config or {}
        
        # 设置ZMQ
        self.context = zmq.Context()
        
        # 消息发布器 (PUB) - 所有机器人共用一个
        self.message_publisher = self.context.socket(zmq.PUB)
        self.message_publisher.bind("tcp://*:5555")

        # 命令接收器 (SUB) - 根据配置创建多个
        self.command_subscribers = {}
        
        # 如果有配置文件，根据配置创建命令订阅器
        if self.config:
            # 遍历配置中的机器人节点
            for robot_name, robot_config in self.config.items():
                if 'port' in robot_config:
                    port = robot_config['port']
                    self.logger.info(f"为 {robot_name} 创建命令订阅器，端口: {port}")
                    
                    # 创建订阅器
                    subscriber = self.context.socket(zmq.SUB)
                    subscriber.bind(f"tcp://*:{port}")
                    subscriber.setsockopt_string(zmq.SUBSCRIBE, "")
                    
                    # 存储订阅器
                    self.command_subscribers[robot_name] = {
                        'socket': subscriber,
                        'config': robot_config
                    }
                    
                    # 为每个订阅器启动一个处理线程
                    thread = threading.Thread(
                        target=self.handle_commands_for_robot,
                        args=(robot_name, subscriber),
                        daemon=True
                    )
                    thread.start()
        else:
            # 如果没有配置，创建默认的命令订阅器
            default_subscriber = self.context.socket(zmq.SUB)
            default_subscriber.bind("tcp://*:5556")
            default_subscriber.setsockopt_string(zmq.SUBSCRIBE, "")
            self.command_subscribers['default'] = {
                'socket': default_subscriber,
                'config': {}
            }
            
            # 启动默认命令处理线程
            self.command_handler = threading.Thread(target=self.dealCommand, daemon=True)
            self.command_handler.start()
        
        # 添加持仓信息缓存
        self.position_cache = []
        self.position_lock = threading.Lock()  # 添加锁以保证线程安全
        self.position_end_event = threading.Event()  # 添加事件用于等待持仓查询完成

        # 添加行情缓存字典
        self.market_data = {}  # 格式: {symbol: {'reqId': reqId, 'bid': price, 'ask': price, 'last': price, 'minTick': minTick}}
        self.market_data_lock = threading.Lock()  # 添加锁以保证线程安全

        # 添加订单相关的数据结构
        self.orders = {}  # {orderId: {'contract': Contract, 'order': Order, 'status': str, ...}}
        self.executions = {}  # {orderId: [execution1, execution2, ...]}
        self.orders_lock = threading.Lock()

        # 修改K线数据存储结构和限制
        self.klines = {}  # {symbol: {'1m': [], '3m': [], '30m': [], '4h': [], '1d': []}}
        self.kline_lock = threading.Lock()
        
        # 定义支持的K线周期（分钟）和存储限制
        self.KLINE_CONFIG = {
            '1m':  {'minutes': 1,    'limit': 1440},  # 1天
            '3m':  {'minutes': 3,    'limit': 960},   # 2天
            '30m': {'minutes': 30,   'limit': 192},   # 4天
            '4h':  {'minutes': 240,  'limit': 24},    # 4天
            '1d':  {'minutes': 1440, 'limit': 10}     # 10天
        }
        
        # K线存储相关配置
        self.kline_storage_path = "kline_data"
        self.save_interval = 60  # 每分钟保存一次
        os.makedirs(self.kline_storage_path, exist_ok=True)
        
        # 启动K线存储线程
        self.kline_saver = threading.Thread(target=self.save_klines_periodically)
        self.kline_saver.daemon = True
        self.kline_saver.start()
        
        # 加载历史K线数据
        self.load_klines()

        # 定义消息分隔符
        self.SEPARATOR = "|||"  # 字段分隔符
        self.MSG_END = "###"    # 消息结束符
        self.hook_prefix = "HOOK"

        # 定义特殊处理的合约列表
        self.SPECIAL_CONTRACTS = {'ES', 'NQ', 'GC', 'SI', 'HG'}
        
        # 定义夏令时和冬令时的交易时间
        self.TRADING_HOURS = {
            'winter': {
                'start': 7,  # 冬令时开盘时间 (7:00)
                'end': 6,    # 次日冬令时收盘时间 (6:00)
            },
            'summer': {
                'start': 6,  # 夏令时开盘时间 (6:00)
                'end': 5,    # 次日夏令时收盘时间 (5:00)
            }
        }

        # 添加标志来控制3分钟K线检测
        self.check_3m_enabled = {}  # 用于存储每个合约的3分钟检测状态
        self.check_3m_lock = threading.Lock()

        self.pattern_configs = {}  # 存储形态检测配置

        # 添加重连相关的属性
        self.reconnect_thread = None
        self.is_connected = False
        self.last_disconnect_date = None
        
        # 启动重连检查线程
        self.reconnect_thread = threading.Thread(target=self.check_reconnect_time, daemon=True)
        self.reconnect_thread.start()

    def format_message(self, msg_type: str, *fields, to_topic=None) -> str:
        """格式化消息
        Args:
            msg_type: 消息类型
            *fields: 消息字段
            to_topic: 指定目标话题，如果为None则发送给所有订阅者
        Returns:
            formatted_msg: "[topic:]msg_type|||field1|||field2|||...###"
        """
        # 基本消息格式
        basic_msg = f"{msg_type}{self.SEPARATOR}{self.SEPARATOR.join(map(str, fields))}{self.MSG_END}"
        
        # 如果指定了目标话题，添加话题前缀
        if to_topic:
            return f"{to_topic}+++{basic_msg}"
        
        return basic_msg

    def reqPositions(self, source_topic=None):
        """请求持仓信息"""
        try:
            # 重置缓存和事件
            with self.position_lock:
                self.position_cache.clear()
            self.position_end_event.clear()
            
            # 存储来源话题，用于处理结果时发送回对应机器人
            self.current_req_topic = source_topic
            
            # 发送请求
            self.app.reqPositions()
            
            # 等待持仓查询完成（最多等待5秒）
            if not self.position_end_event.wait(5):
                error_msg = self.format_message("ERROR", "查询持仓超时", to_topic=source_topic)
                self.message_publisher.send_string(error_msg)
                
        except Exception as e:
            self.logger.error(f"请求持仓失败: {e}")
            error_msg = self.format_message("ERROR", f"请求持仓失败: {str(e)}", to_topic=source_topic)
            self.message_publisher.send_string(error_msg)

    def reqOpenOrders(self):
        """请求开放订单信息"""
        try:
            # 清空现有订单数据
            with self.orders_lock:
                self.orders.clear()
                self.executions.clear()
            
            # 发送请求
            self.app.reqAllOpenOrders()
            
            # 记录日志
            self.logger.info("已发送查询开放订单请求")
            
        except Exception as e:
            self.logger.error(f"请求开放订单失败: {e}")
            error_msg = self.format_message("ERROR", f"请求开放订单失败: {str(e)}")
            self.message_publisher.send_string(error_msg)

    def connect(self):
        """连接到IB服务器"""
        try:
            self.app.connect("127.0.0.1", 7497, 0)
        except Exception as e:
            self.logger.error(f"连接IB服务器失败: {e}")
            self.is_connected = False

    def handle_connection(self):
        """处理连接回调"""
        self.logger.info("已连接到IB服务器")
        self.is_connected = True
        self.message_publisher.send_string(self.format_message("SYSTEM", "已连接到IB服务器"))

    def handle_connection_closed(self):
        """处理连接关闭回调"""
        self.logger.info("已断开与IB服务器的连接")
        self.is_connected = False
        self.message_publisher.send_string(self.format_message("SYSTEM", "已断开与IB服务器的连接"))

    def load_contracts(self, folder_path):
        for root, dirs, files in os.walk(folder_path):
            if len(files) == 0:
                print(f"请先配置合约文件")
                continue
            for file in files:
                file_path = os.path.join(root, file)
                with open(file_path, 'r') as f:
                    contract_data = json.load(f)
                    contract = Contract()
                    contract.symbol = contract_data.get("Symbol", "")
                    contract.secType = contract_data.get("SecType", "")
                    contract.exchange = contract_data.get("Exchange", "")
                    contract.currency = contract_data.get("Currency", "")
                    contract.lastTradeDateOrContractMonth = contract_data.get("LastTradeDateOrContractMonth", "")
                    contract.multiplier = contract_data.get("Multiplier", "")

                    # 保存形态检测配置
                    self.pattern_configs[contract.symbol] = contract_data.get('patterns', {
                        '3m': {
                            'down_to_up': 1,
                            'up_to_down': 1,
                            'upper_shadow': 1,
                            'lower_shadow': 1
                        },
                        '30m': {
                            'down_to_up': 1,
                            'up_to_down': 1,
                            'upper_shadow': 1,
                            'lower_shadow': 1
                        },
                        '4h': {
                            'down_to_up': 1,
                            'up_to_down': 1,
                            'upper_shadow': 1,
                            'lower_shadow': 1
                        }
                    })

                    self.contracts[contract.symbol] = contract

    def handle_tick_price(self, reqId: int, tickType: int, price: float, attrib):
        """处理行情价格回调"""
        try:
            # 获取合约信息
            contract = None
            for c in self.contracts.values():
                if reqId == self.market_data.get(c.symbol, {}).get('reqId'):
                    contract = c
                    break
            
            if not contract:
                self.logger.warning(f"未找到reqId对应的合约: {reqId}")
                return

            with self.market_data_lock:
                # 确保合约的数据字典存在
                if contract.symbol not in self.market_data:
                    self.market_data[contract.symbol] = {
                        'reqId': reqId,
                        'bid': 0.0,
                        'ask': 0.0,
                        'last': 0.0,
                        'minTick': 0.0
                    }
                
                # 更新相应价格
                if tickType == 1:  # 买价
                    self.market_data[contract.symbol]['bid'] = price
                    self.logger.debug(f"{contract.symbol} Bid: {price}")
                elif tickType == 2:  # 卖价
                    self.market_data[contract.symbol]['ask'] = price
                    self.logger.debug(f"{contract.symbol} Ask: {price}")
                elif tickType == 4:  # 最新价
                    self.market_data[contract.symbol]['last'] = price
                    self.logger.debug(f"{contract.symbol} Last: {price}")
                
                if tickType == 4:
                    # 添加K线更新
                    self.update_kline(
                        contract.symbol,
                        price,
                        attrib.volume if hasattr(attrib, 'volume') else 0,
                        time.time()
                    )
                
        except Exception as e:
            self.logger.error(f"处理行情数据失败: {e}")

    def handle_tick_req_params(self, tickerId: int, minTick: float, 
                             bboExchange: str, snapshotPermissions: int):
        """处理合约参数回调"""
        try:
            # 获取合约信息
            contract = None
            for c in self.contracts.values():
                if tickerId == self.market_data.get(c.symbol, {}).get('reqId'):
                    contract = c
                    break
            
            if not contract:
                self.logger.warning(f"未找到reqId对应的合约: {tickerId}")
                return

            with self.market_data_lock:
                # 确保合约的数据字典存在
                if contract.symbol in self.market_data:
                    # 如果market_data中已有此tickerId的数据，添加参数信息
                    self.market_data[contract.symbol]['minTick'] = minTick
                
                # 记录日志
                symbol = self.market_data.get(tickerId, {}).get('symbol', 'Unknown')
                self.logger.debug(
                    f"合约参数更新 - {symbol}: "
                    f"MinTick: {minTick}, "
                )
                
        except Exception as e:
            self.logger.error(f"处理合约参数失败: {e}")

    def handle_position(self, account: str, contract, pos: float, avgCost: float):
        """处理持仓回调"""
        # 只处理期货持仓
        if contract.secType != "FUT":
            return
            
        # 将持仓信息添加到缓存
        with self.position_lock:
            self.position_cache.append({
                'account': account,
                'symbol': contract.symbol,
                'secType': contract.secType,
                'exchange': contract.exchange,
                'position': pos,
                'avgCost': avgCost
            })

    def handle_position_end(self):
        """处理持仓查询结束回调"""
        try:
            with self.position_lock:
                if not self.position_cache:
                    # 没有持仓时发送空持仓消息
                    msg = self.format_message("POSITION", "当前没有持仓", to_topic=self.current_req_topic)
                else:
                    # 格式化所有持仓信息
                    position_text = "当前持仓：\n"
                    # 按品种名称排序
                    sorted_positions = sorted(self.position_cache, key=lambda x: x['symbol'])
                    
                    avg_cost = 0
                    for pos in sorted_positions:
                        avg_cost = pos['avgCost'] / self.contracts[pos['symbol']].multiplier
                        position_text += (
                            f"- {pos['symbol']}: "
                            f"{pos['position']}手 @ {avg_cost:.2f}\n"
                        )
                    msg = self.format_message("POSITION", position_text, to_topic=self.current_req_topic)
                
                # 清空缓存
                self.position_cache.clear()
                
                # 发送合并后的消息
                self.message_publisher.send_string(msg)
                
        except Exception as e:
            self.logger.error(f"处理持仓信息失败: {e}")
            error_msg = self.format_message("ERROR", f"处理持仓信息出错: {str(e)}", to_topic=self.current_req_topic)
            self.message_publisher.send_string(error_msg)
        finally:
            # 标记持仓查询完成
            self.position_end_event.set()
            # 清除当前请求话题
            self.current_req_topic = None

    def get_market_price(self, symbol: str) -> dict:
        """获取指定合约的行情数据"""
        with self.market_data_lock:
            return self.market_data.get(symbol, {
                'reqId': 0,
                'bid': 0.0,
                'ask': 0.0,
                'last': 0.0,
                'minTick': 0.0
            })

    def subscribe_market_data(self, contracts: dict):
        """订阅行情数据"""
        try:
            # self.app.reqMarketDataType(1)  # 请求实时行情
            self.app.reqMarketDataType(3)  # 请求延时行情
            
            for contract in contracts.values():
                self.reqId += 1
                # 记录reqId和合约的对应关系
                with self.market_data_lock:
                    self.market_data[contract.symbol] = {
                        'reqId': self.reqId,
                        'bid': 0.0,
                        'ask': 0.0,
                        'last': 0.0,
                        'minTick': 0.0
                    }
                
                self.app.reqMktData(
                    self.reqId, 
                    contract, 
                    "", # genericTickList
                    False, # snapshot
                    False, # regulatorySnapshot
                    [] # mktDataOptions
                )
                self.logger.info(f"订阅行情: {contract.symbol}")
                
        except Exception as e:
            self.logger.error(f"订阅行情失败: {e}")
            error_msg = self.format_message("ERROR", f"订阅行情失败: {str(e)}")
            self.message_publisher.send_string(error_msg)

    def unsubscribe_market_data(self, symbol: str = None):
        """取消行情订阅"""
        try:
            with self.market_data_lock:
                if symbol:
                    # 取消单个合约的订阅
                    if symbol in self.market_data:
                        reqId = self.market_data[symbol]['reqId']
                        self.app.cancelMktData(reqId)
                        del self.market_data[symbol]
                        self.logger.info(f"取消行情订阅: {symbol}")
                else:
                    # 取消所有订阅
                    for symbol, data in self.market_data.items():
                        self.app.cancelMktData(data['reqId'])
                        self.logger.info(f"取消行情订阅: {symbol}")
                    self.market_data.clear()
                    
        except Exception as e:
            self.logger.error(f"取消行情订阅失败: {e}")
            error_msg = self.format_message("ERROR", f"取消行情订阅失败: {str(e)}")
            self.message_publisher.send_string(error_msg)

    def run(self):
        self.app.run()

    def send_trading_command_help(self, source_topic=None):
        """发送交易指令帮助信息"""
        trading_help = """
交易指令格式说明:

1. 下单指令:
   - 买入: ac b [品种] [类型] [数量] [价格]
      例如: ac b ES LMT 1 4200  (以限价4200买入1手ES期货)
      例如: ac b ES LMT 1 (以现价+20tick买入1手ES期货)
      例如: ac b ES MKT 1  (以市价买入1手ES期货)
      例如: ac b ES STP 1 4200 (以止损4200买入1手ES期货)
                  
   - 卖出: ac s [品种] [类型] [数量] [价格]
      例如: ac s ES LMT 1 4210  (以限价4210卖出1手ES期货)
      例如: ac s ES LMT 1 (以现价-20tick卖出1手ES期货)
      例如: ac s ES MKT 1  (以市价卖出1手ES期货)
      例如: ac s ES STP 1 4210 (以止损4210卖出1手ES期货)

2. 撤单指令:
   - ac c [订单号]
      例如: ac c 123456  (撤销订单号为123456的委托单)

订单类型说明:
- LMT: 限价单 (可以指定价格，也可以不指定价格，不指定时使用市场价格加减一定点数)
- MKT: 市价单 (不需要指定价格)
- STP: 止损单 (需要指定触发价格)

注意:
- 品种名称区分大小写，通常使用大写
- 价格根据实际行情填写
- 数量必须为整数
"""
        self.message_publisher.send_string(self.format_message("TRADING_HELP", trading_help, to_topic=source_topic))
        return

    def dealCommand(self, command_text, robot_name='default'):
        """处理命令队列中的指令"""
        try:
            # 检查命令是否包含来源话题
            source_topic = None
            content = command_text
            if "+++" in command_text:
                source_topic, content = command_text.split("+++", 1)
                self.logger.info(f"来自话题 {source_topic} 的指令: {content}")
            
            # 将指令文本分割成字段
            fields = content.strip().split()
            if not fields:
                self.logger.warning("指令为空")
                self.send_command_list(source_topic)
                return
                
            command = fields[0].lower()  # 第一个字段是指令类型
            
            # 查询持仓指令
            if command == "qs":
                self.logger.info("执行查询持仓")
                self.reqPositions(source_topic)

            # 查询order指令
            elif command == "qo":
                self.logger.info("执行查询OpenOrder")
                self.reqOpenOrders()
            
            elif command == "rq":
                self.logger.info("请求下一个请求ID")
                self.get_next_reqId()
            
            # 查询合约报价指令
            elif command == "qh" and len(fields) == 2:
                symbol = fields[1].upper()
                self.logger.info(f"查询合约报价: {symbol}")
                self.query_contract_price(symbol, source_topic)
            
            # 交易相关指令
            elif command.startswith("ac"):
                # 检查是否有足够的参数
                if len(fields) < 2:
                    error_msg = "交易指令格式错误，缺少操作类型"
                    self.logger.warning(error_msg)
                    self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                    self.send_trading_command_help(source_topic)
                    return
                    
                action = fields[1].lower()
                
                # 检查操作类型是否支持
                if action not in ["b", "s", "c"]:
                    error_msg = f"不支持的交易操作: {action}，支持的操作有: b(买入), s(卖出), c(撤单)"
                    self.logger.warning(error_msg)
                    self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                    self.send_trading_command_help(source_topic)
                    return
                
                # 买入或卖出指令
                if action in ["b", "s"]:
                    # 检查参数数量
                    if len(fields) < 5:
                        error_msg = f"交易指令参数不足，格式: ac {action} [品种] [类型] [数量] [价格]"
                        self.logger.warning(error_msg)
                        self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                        self.send_trading_command_help(source_topic)
                        return
                        
                    # 检查品种是否存在
                    symbol = fields[2].upper()
                    if symbol not in self.market_data:
                        error_msg = f"未找到品种: {symbol}，请检查品种名称是否正确"
                        self.logger.warning(error_msg)
                        self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                        self.send_trading_command_help(source_topic)
                        return
                    
                    # 检查订单类型
                    order_type = fields[3].upper()
                    if order_type not in ["LMT", "MKT", "STP"]:
                        error_msg = f"不支持的订单类型: {order_type}，支持的类型有: LMT(限价), MKT(市价), STP(止损)"
                        self.logger.warning(error_msg)
                        self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                        self.send_trading_command_help(source_topic)
                        return
                    
                    # 处理订单数量
                    try:
                        quantity = int(fields[4])
                        if quantity <= 0:
                            raise ValueError("数量必须为正整数")
                    except ValueError as e:
                        error_msg = f"数量格式错误: {fields[4]}, {str(e)}"
                        self.logger.warning(error_msg)
                        self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                        self.send_trading_command_help(source_topic)
                        return
                    
                    # 处理价格(根据订单类型)
                    price = 0
                    if order_type == "STP":
                        # 止损单必须指定价格
                        if len(fields) < 6:
                            error_msg = f"STP订单必须指定触发价格"
                            self.logger.warning(error_msg)
                            self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                            self.send_trading_command_help(source_topic)
                            return
                            
                        try:
                            price = float(fields[5])
                            if price <= 0:
                                raise ValueError("价格必须为正数")
                        except ValueError as e:
                            error_msg = f"价格格式错误: {fields[5]}, {str(e)}"
                            self.logger.warning(error_msg)
                            self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                            self.send_trading_command_help(source_topic)
                            return
                    elif order_type == "LMT":
                        # 限价单可以不指定价格，不指定时使用市场价格加减一定点数
                        if len(fields) >= 6:
                            try:
                                price = float(fields[5])
                            except ValueError:
                                error_msg = f"价格格式错误: {fields[5]}"
                                self.logger.warning(error_msg)
                                self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                                self.send_trading_command_help(source_topic)
                                return
                        else:
                            # 不指定价格时，使用市场价格加减一定点数
                            price = 0  # 标记为0，后面会根据买卖方向设置实际价格
                    elif order_type == "MKT":
                        # 市价单忽略价格参数
                        price = 0
                    
                    # 查找合约
                    contract = self.contracts.get(symbol)
                    if not contract:
                        error_msg = f"未找到合约: {symbol}"
                        self.logger.warning(error_msg)
                        self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                        self.send_trading_command_help(source_topic)
                        return
                    
                    # 创建订单
                    order = Order()
                    order.action = "BUY" if action == "b" else "SELL"
                    order.totalQuantity = quantity
                    order.orderType = order_type
                    order.outsideRth = True
                    if price <= 0:
                        if action == "b":
                            order.lmtPrice = self.market_data[symbol]['ask'] + self.market_data[symbol]["minTick"] * 20
                        else:
                            order.lmtPrice = self.market_data[symbol]['bid'] - self.market_data[symbol]["minTick"] * 20
                    if price > 0:
                        order.lmtPrice = price

                    if order_type == "stp" or order_type == "STP":
                        order.auxPrice = price
                    
                    # 发送订单
                    self.orderId += 1
                    self.app.placeOrder(self.orderId, contract, order)
                    
                    # 记录日志
                    if order_type == "stp" or order_type == "STP":
                        log_msg = f"已发送订单: {order.action} {symbol} {quantity}手 STP @ {price}"
                    elif order_type == "lmt" or order_type == "LMT":
                        log_msg = f"已发送订单: {order.action} {symbol} {quantity}手 LMT @ {price}"
                    elif order_type == "mkt" or order_type == "MKT":
                        log_msg = f"已发送订单: {order.action} {symbol} {quantity}手 MKT"
                    else:
                        log_msg = f"已发送订单: {order.action} {symbol} {quantity}手 未知类型"
                    self.logger.info(log_msg)
                    # 修正: 使用格式化消息并添加来源话题
                    self.message_publisher.send_string(self.format_message("ORDER", log_msg, to_topic=source_topic))
                    
                # 撤单指令
                elif action == "c":
                    if len(fields) != 3:
                        error_msg = "撤单指令格式错误: ac c [订单号]"
                        self.logger.warning(error_msg)
                        self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                        self.send_trading_command_help(source_topic)
                        return
                        
                    try:
                        order_id = int(fields[2])
                        self.app.cancelOrder(order_id)
                        success_msg = f"已发送撤单指令: {order_id}"
                        self.logger.info(success_msg)
                        self.message_publisher.send_string(self.format_message("INFO", success_msg, to_topic=source_topic))
                    except ValueError:
                        error_msg = f"订单号格式错误: {fields[2]}, 应为整数"
                        self.logger.warning(error_msg)
                        self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                        self.send_trading_command_help(source_topic)
                    return
            
            # 指令列表查询
            elif command == "help" or command == "?":
                if len(fields) > 1 and fields[1].lower() == "trade":
                    # 专门查询交易指令帮助
                    self.send_trading_command_help(source_topic)
                else:
                    # 查询所有指令帮助
                    self.send_command_list(source_topic)
            
            else:
                error_msg = f"不支持的指令: {command}"
                self.logger.warning(error_msg)
                # 发送错误信息，并附带指令列表
                self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                self.send_command_list(source_topic)
            
        except Exception as e:
            error_msg = f"处理命令失败: {str(e)}"
            self.logger.error(error_msg)
            # 发送错误消息时也指定来源话题
            self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
            # 发送帮助信息
            if command_text.strip().lower().startswith("ac"):
                self.send_trading_command_help(source_topic)
            else:
                self.send_command_list(source_topic)

    def query_contract_price(self, symbol: str, source_topic=None):
        """查询合约的当前报价"""
        try:
            market_data = self.get_market_price(symbol)
            if market_data['reqId'] == 0:
                error_msg = f"未找到合约: {symbol}"
                self.logger.warning(error_msg)
                self.message_publisher.send_string(self.format_message("ERROR", error_msg, to_topic=source_topic))
                return
            
            price_info = (
                f"合约: {symbol}\n"
                f"买价: {market_data['bid']}\n"
                f"卖价: {market_data['ask']}\n"
                f"最新价: {market_data['last']}\n"
            )
            self.message_publisher.send_string(self.format_message("PRICE", price_info, to_topic=source_topic))
        
        except Exception as e:
            self.logger.error(f"查询合约报价失败: {e}")
            error_msg = self.format_message("ERROR", f"查询合约报价失败: {str(e)}", to_topic=source_topic)
            self.message_publisher.send_string(error_msg)

    def dealMessage(self, message: str):
        """发送消息"""
        self.logger.info(f"发送消息: {message}")
        self.message_publisher.send_string(message)

    def handle_order_status(self, orderId: int, status: str, filled: float,
                          remaining: float, avgFillPrice: float, lastFillPrice: float):
        """处理订单状态更新"""
        try:
            with self.orders_lock:
                if orderId not in self.orders:
                    self.orders[orderId] = {}
                
                # 更新订单信息
                self.orders[orderId].update({
                    'status': status,
                    'filled': filled,
                    'remaining': remaining,
                    'avgFillPrice': avgFillPrice,
                    'lastFillPrice': lastFillPrice
                })
                
                # 构造状态消息
                msg = (
                    f"订单状态更新 - OrderId: {orderId}\n"
                    f"状态: {status}\n"
                    f"已成交: {filled}\n"
                    f"剩余: {remaining}\n"
                    f"成交均价: {avgFillPrice}\n"
                    f"最新成交价: {lastFillPrice}"
                )
                
                # 使用webhook发送订单状态
                self.send_order_status(self.format_message("ORDER_STATUS", msg))
                
                # 同时发送到ZMQ（保持原有功能）
                self.message_publisher.send_string(self.format_message("ORDER_STATUS", msg))
            
                # 记录日志
                self.logger.info(f"发送消息: {msg}")
                
        except Exception as e:
            self.logger.error(f"处理订单状态失败: {e}")

    def handle_open_order(self, orderId: int, contract: Contract, 
                         order: Order, orderState: OrderState):
        """处理开放订单信息"""
        try:
            with self.orders_lock:
                self.orders[orderId] = {
                    'contract': contract,
                    'order': order,
                    'state': orderState,
                    'status': orderState.status
                }
                
                # 构造订单消息
                msg = (
                    f"订单信息 - OrderId: {orderId}\n"
                    f"合约: {contract.symbol}\n"
                    f"方向: {order.action}\n"
                    f"数量: {order.totalQuantity}\n"
                    f"类型: {order.orderType}\n"
                    f"价格: {order.lmtPrice if order.orderType == 'LMT' else 'MKT'}\n"
                    f"状态: {orderState.status}"
                )
                
                self.message_publisher.send_string(self.format_message("OPEN_ORDER", msg))
                
        except Exception as e:
            self.logger.error(f"处理开放订单失败: {e}")

    def handle_execution(self, reqId: int, contract: Contract, execution: Execution):
        """处理成交回报"""
        try:
            orderId = execution.orderId
            with self.orders_lock:
                if orderId not in self.executions:
                    self.executions[orderId] = []
                self.executions[orderId].append(execution)
                
                # 构造成交消息
                msg = (
                    f"成交回报 - OrderId: {orderId}\n"
                    f"合约: {contract.symbol}\n"
                    f"成交量: {execution.shares}\n"
                    f"成交价: {execution.price}\n"
                    f"方向: {execution.side}\n"
                    f"时间: {execution.time}"
                )
                
                self.message_publisher.send_string(self.format_message("EXECUTION", msg))
                
        except Exception as e:
            self.logger.error(f"处理成交回报失败: {e}")

    def get_order_info(self, orderId: int) -> dict:
        """获取订单信息"""
        try:
            with self.orders_lock:
                order_info = self.orders.get(orderId, {}).copy()
                executions = self.executions.get(orderId, [])
                if order_info:
                    order_info['executions'] = executions
                return order_info
        except Exception as e:
            self.logger.error(f"获取订单信息失败: {e}")
            return {}

    def get_all_orders(self) -> dict:
        """获取有订单信息"""
        try:
            with self.orders_lock:
                return {
                    orderId: {
                        **order_info.copy(),
                        'executions': self.executions.get(orderId, [])
                    }
                    for orderId, order_info in self.orders.items()
                }
        except Exception as e:
            self.logger.error(f"获取所有订单信息失败: {e}")
            return {}

    def get_next_reqId(self):
        """获取下一个请求ID"""
        self.logger.info("获取下一个OrderID")
        return self.app.reqIds(self.reqId)

    def set_next_order_id(self, orderId: int):
        """设置下一个请求ID"""
        self.logger.info(f"设置下一个请求ID: {orderId}")
        self.orderId = orderId

        self.message_publisher.send_string(self.format_message("NEXT_ORDER_ID", f"下一个OrderID: {orderId}"))

    def __del__(self):
        """清理ZMQ资源"""
        self.command_subscriber.close()
        self.message_publisher.close()
        self.context.term()

    def handle_commission_report(self, commissionReport):
        """处理佣金报告"""
        try:
            with self.orders_lock:
                # 查找对应的订单
                for orderId, executions in self.executions.items():
                    for execution in executions:
                        if execution.execId == commissionReport.execId:
                            # 将佣金信息添加到执行记录中
                            execution.commission = commissionReport.commission
                            execution.realizedPNL = commissionReport.realizedPNL
                            
                            # 构造佣金消息
                            msg = (
                                f"佣金报告 - OrderId: {orderId}\n"
                                f"成交编号: {commissionReport.execId}\n"
                                f"佣金: {commissionReport.commission}\n"
                                f"币种: {commissionReport.currency}\n"
                                f"已实现盈亏: {commissionReport.realizedPNL}"
                            )
                            
                            self.message_publisher.send_string(self.format_message("COMMISSION", msg))
                            return
                            
        except Exception as e:
            self.logger.error(f"处理佣金报告失败: {e}")

    def update_kline(self, symbol: str, price: float, volume: float, timestamp: float):
        """更新K线数据"""
        try:
            with self.kline_lock:
                # 初始化该symbol的所有周期K线存储
                if symbol not in self.klines:
                    self.klines[symbol] = {period: [] for period in self.KLINE_CONFIG.keys()}
                
                # 更新所有周期的K线
                for period in self.KLINE_CONFIG.keys():
                    # 使用calculate_kline_timestamp计算正确的时间戳
                    period_timestamp = self.calculate_kline_timestamp(symbol, timestamp, period)
                    self._update_period_kline(symbol, period, price, volume, period_timestamp)
                    
        except Exception as e:
            self.logger.error(f"更新K线数据失败: {e}")

    def _update_period_kline(self, symbol: str, period: str, price: float, volume: float, period_timestamp: int):
        """更新指定周期的K线"""
        try:
            klines = self.klines[symbol][period]
            limit = self.KLINE_CONFIG[period]['limit']
            
            # 检查是否需要创建新的K线
            if not klines or klines[-1]['timestamp'] < period_timestamp:
                # 在创建新K线之前，检查前一根K线的形态
                if period == '30m' and len(klines) >= 3:
                    self.check_30m_pattern(symbol, klines)
                elif period == '3m' and len(klines) >= 3:
                    # 只有在启用状态下才检查3分钟K线形态
                    with self.check_3m_lock:
                        if self.check_3m_enabled.get(symbol, False):
                            self.check_3m_pattern(symbol, klines)
                elif period == '4h' and len(klines) >= 4:
                    self.check_4h_pattern(symbol, klines)
                
                # 创建新的K线
                new_kline = {
                    'timestamp': period_timestamp,
                    'open': price,
                    'high': price,
                    'low': price,
                    'close': price,
                    'volume': volume
                }
                klines.append(new_kline)
                
                # 限制储的K线数量
                if len(klines) > limit:
                    klines.pop(0)
            else:
                # 更新当前K线
                current_kline = klines[-1]
                current_kline['high'] = max(current_kline['high'], price)
                current_kline['low'] = min(current_kline['low'], price)
                current_kline['close'] = price
                current_kline['volume'] += volume
                
        except Exception as e:
            self.logger.error(f"更新K线数据失败: {e}")

    def has_long_upper_shadow(self, kline: dict) -> bool:
        """检查是否有长上影线
        条件：上影线长度超过实体长度的2倍
        """
        try:
            body_length = abs(kline['close'] - kline['open'])
            upper_shadow = kline['high'] - max(kline['open'], kline['close'])
            
            # 防止除以0
            if body_length == 0:
                return upper_shadow > 0
            
            return upper_shadow / body_length > 2.0
            
        except Exception as e:
            self.logger.error(f"检查长上影线失败: {e}")
            return False

    def check_pattern(self, symbol: str, period: str, klines: list, min_consecutive: int = 2):
        """检查K线形态"""
        try:
            if len(klines) < min_consecutive + 1:
                return
            
            # 获取该合约该周期的形态配置
            pattern_config = self.pattern_configs.get(symbol, {}).get(period, {})
            if not pattern_config:
                return
            
            recent_klines = klines[-(min_consecutive+1):]
            latest_kline = recent_klines[-1]
            
            # 检查连续阴线后的阳线
            if pattern_config.get('down_to_up', 0) == 1:
                consecutive_down = 0
                for kline in recent_klines[:-1]:
                    if self.is_down_candle(kline):
                        consecutive_down += 1
                    else:
                        break
                    
                if consecutive_down >= min_consecutive and self.is_up_candle(latest_kline):
                    price_change = (latest_kline['close'] - latest_kline['open']) / latest_kline['open'] * 100
                    additional_info = f"连续{consecutive_down}根阴线后出现阳线\n阳线涨幅: {price_change:.2f}%"
                    
                    self.send_pattern_alert(
                        symbol=symbol,
                        period=period,
                        pattern_type="阴线反转阳线",
                        kline_data=latest_kline,
                        additional_info=additional_info
                    )
            
            # 检查连续阳线后的阴线
            if pattern_config.get('up_to_down', 0) == 1:
                consecutive_up = 0
                for kline in recent_klines[:-1]:
                    if self.is_up_candle(kline):
                        consecutive_up += 1
                    else:
                        break
                    
                if consecutive_up >= min_consecutive and self.is_down_candle(latest_kline):
                    price_change = (latest_kline['close'] - latest_kline['open']) / latest_kline['open'] * 100
                    additional_info = f"连续{consecutive_up}根阳线后出现阴线\n阴线跌幅: {price_change:.2f}%"
                    
                    self.send_pattern_alert(
                        symbol=symbol,
                        period=period,
                        pattern_type="阳线反转阴线",
                        kline_data=latest_kline,
                        additional_info=additional_info
                    )
            
            # 检查长下影线
            if pattern_config.get('lower_shadow', 0) == 1 and self.has_long_lower_shadow(latest_kline):
                body_length = abs(latest_kline['close'] - latest_kline['open'])
                lower_shadow = min(latest_kline['open'], latest_kline['close']) - latest_kline['low']
                shadow_ratio = lower_shadow / body_length if body_length > 0 else float('inf')
                
                self.send_long_shadow_alert(
                    symbol=symbol,
                    period=period,
                    kline_data=latest_kline,
                    shadow_ratio=shadow_ratio,
                    shadow_type="下影线"
                )
            
            # 检查长上影线
            if pattern_config.get('upper_shadow', 0) == 1 and self.has_long_upper_shadow(latest_kline):
                body_length = abs(latest_kline['close'] - latest_kline['open'])
                upper_shadow = latest_kline['high'] - max(latest_kline['open'], latest_kline['close'])
                shadow_ratio = upper_shadow / body_length if body_length > 0 else float('inf')
                
                self.send_long_shadow_alert(
                    symbol=symbol,
                    period=period,
                    kline_data=latest_kline,
                    shadow_ratio=shadow_ratio,
                    shadow_type="上影线"
                )
                
        except Exception as e:
            self.logger.error(f"检查K线形态失败: {e}")

    def check_3m_pattern(self, symbol: str, klines: list):
        """检查3分钟K线形态"""
        self.check_pattern(symbol, "3m", klines, min_consecutive=2)

    def check_30m_pattern(self, symbol: str, klines: list):
        """检查30分钟K线形态"""
        self.check_pattern(symbol, "30m", klines, min_consecutive=2)

    def check_4h_pattern(self, symbol: str, klines: list):
        """检查4小时K线形态"""
        self.check_pattern(symbol, "4h", klines, min_consecutive=2)

    def save_klines_periodically(self):
        """定期保存K线数据"""
        while True:
            try:
                self.save_klines()
                time.sleep(self.save_interval)
            except Exception as e:
                self.logger.error(f"定期保存K线数据失败: {e}")

    def save_klines(self):
        """保存K线数据到文件"""
        try:
            with self.kline_lock:
                for symbol, periods in self.klines.items():
                    # 为每个周期创建单独的文件
                    for period, klines in periods.items():
                        file_path = os.path.join(
                            self.kline_storage_path, 
                            f"{symbol}_{period}.json"
                        )
                        
                        kline_data = {
                            'symbol': symbol,
                            'period': period,
                            'data': klines,
                            'last_update': int(time.time())
                        }
                        
                        with open(file_path, 'w') as f:
                            json.dump(kline_data, f, indent=2)
                            
                self.logger.debug("K线数据已保存")
                            
        except Exception as e:
            self.logger.error(f"保存K线数据失败: {e}")

    def load_klines(self):
        """加载历史K线数据"""
        try:
            for file_name in os.listdir(self.kline_storage_path):
                if not file_name.endswith('.json'):
                    continue
                    
                file_path = os.path.join(self.kline_storage_path, file_name)
                with open(file_path, 'r') as f:
                    kline_data = json.load(f)
                    
                symbol = kline_data['symbol']
                period = kline_data['period']
                
                with self.kline_lock:
                    if symbol not in self.klines:
                        self.klines[symbol] = {p: [] for p in self.KLINE_CONFIG.keys()}
                    
                    # 只保留指定数量的K线
                    limit = self.KLINE_CONFIG[period]['limit']
                    self.klines[symbol][period] = kline_data['data'][-limit:]
                    
            self.logger.info("历史K线数据加载完成")
                    
        except Exception as e:
            self.logger.error(f"加载历史K线数据失败: {e}")

    def is_dst(self, timestamp):
        """判断给定时间戳是否是夏令时"""
        dt = datetime.fromtimestamp(timestamp, pytz.timezone('America/New_York'))
        return dt.dst() != timedelta(0)

    def calculate_kline_timestamp(self, symbol: str, timestamp: float, period: str) -> int:
        """计算K线的规范化时间戳"""
        try:
            dt = datetime.fromtimestamp(timestamp)
            
            if period == '1m':
                # 1分钟K线，向下取整到分钟
                return int(timestamp // 60 * 60)
                
            elif period == '3m':
                # 3分钟K线，向下取整到最近的3分钟
                minutes = dt.hour * 60 + dt.minute
                adjusted_minutes = (minutes // 3) * 3
                return int(dt.replace(
                    hour=adjusted_minutes // 60,
                    minute=adjusted_minutes % 60,
                    second=0,
                    microsecond=0
                ).timestamp())
                
            elif period == '30m':
                # 30分钟K线，只取0分和30分
                minutes = dt.hour * 60 + dt.minute
                adjusted_minutes = (minutes // 30) * 30
                return int(dt.replace(
                    hour=adjusted_minutes // 60,
                    minute=adjusted_minutes % 60,
                    second=0,
                    microsecond=0
                ).timestamp())
                
            elif period == '4h' and symbol in self.SPECIAL_CONTRACTS:
                # 获取是否是夏令时
                is_summer = self.is_dst(timestamp)
                start_hour = self.TRADING_HOURS['summer' if is_summer else 'winter']['start']

                # 调整到交易日开始时间
                dt_start = dt.replace(hour=start_hour, minute=0, second=0, microsecond=0)
                if dt.hour < start_hour:
                    dt_start -= timedelta(days=1)
                
                # 计算从开盘时间开始的4小时间隔
                elapsed_hours = (dt - dt_start).total_seconds() / 3600
                intervals = int(elapsed_hours // 4)
                
                # 计算K线时间戳
                kline_dt = dt_start + timedelta(hours=intervals * 4)
                return int(kline_dt.timestamp())
                
            elif period == '1d' and symbol in self.SPECIAL_CONTRACTS:
                # 获取是否是夏令时
                is_summer = self.is_dst(timestamp)
                start_hour = self.TRADING_HOURS['summer' if is_summer else 'winter']['start']
                end_hour = self.TRADING_HOURS['summer' if is_summer else 'winter']['end']
                
                # 调整到交易日开始时间
                dt_start = dt.replace(hour=start_hour, minute=0, second=0, microsecond=0)
                if dt.hour < start_hour:
                    dt_start -= timedelta(days=1)
                
                return int(dt_start.timestamp())
                
            else:
                # 其他情况，使用标准时间戳计算
                return int(timestamp // (self.KLINE_CONFIG[period]['minutes'] * 60) * 
                         (self.KLINE_CONFIG[period]['minutes'] * 60))
                
        except Exception as e:
            self.logger.error(f"计算K线时间戳失败: {e}")
            return int(timestamp)

    def is_kline_complete(self, symbol: str, period: str, kline_timestamp: int) -> bool:
        """判断K线是否已完成"""
        try:
            current_time = time.time()
            
            if period == '4h' and symbol in self.SPECIAL_CONTRACTS:
                # 获取是否是夏令时
                is_summer = self.is_dst(current_time)
                end_hour = self.TRADING_HOURS['summer' if is_summer else 'winter']['end']
                
                # 获取K线结束时间
                kline_end = kline_timestamp + 4 * 3600
                kline_dt = datetime.fromtimestamp(kline_end)
                
                # 如果是最后一根K线，检查是否到达收盘时间
                next_day = kline_dt.replace(hour=end_hour, minute=0, second=0)
                if kline_end > next_day.timestamp():
                    return current_time >= next_day.timestamp()
                
                return current_time >= kline_end
                
            elif period == '1d' and symbol in self.SPECIAL_CONTRACTS:
                # 获取是否是夏令时
                is_summer = self.is_dst(current_time)
                end_hour = self.TRADING_HOURS['summer' if is_summer else 'winter']['end']
                
                # 计算交易日结束时间
                kline_dt = datetime.fromtimestamp(kline_timestamp)
                session_end = (kline_dt + timedelta(days=1)).replace(
                    hour=end_hour, minute=0, second=0)
                
                return current_time >= session_end.timestamp()
                
            else:
                # 其他周期的K线，直接比较时间
                period_seconds = self.KLINE_CONFIG[period]['minutes'] * 60
                return current_time >= (kline_timestamp + period_seconds)
                
        except Exception as e:
            self.logger.error(f"判断K线完成状态失败: {e}")
            return False

    def is_down_candle(self, kline):
        """检查是否为阴线"""
        return kline['close'] < kline['open']

    def is_up_candle(self, kline):
        """检查是否为阳线"""
        return kline['close'] > kline['open']

    def has_long_lower_shadow(self, kline):
        """检查是否有长下影线
        条件：下影线长度超过实体长度的2倍
        """
        body_length = abs(kline['close'] - kline['open'])
        lower_shadow = min(kline['open'], kline['close']) - kline['low']
        
        # 防止除以0
        if body_length == 0:
            return lower_shadow > 0
            
        return lower_shadow / body_length > 2.0

    def send_pattern_alert(self, symbol: str, period: str, pattern_type: str, 
                          kline_data: dict, additional_info: str = "") -> bool:
        """
        发送K线形态提醒
        Args:
            symbol: 合约代码
            period: K线周期
            pattern_type: 形态类型
            kline_data: K线数据
            additional_info: 额外信息
        Returns:
            是否发送成功
        """
        try:
            time_str = datetime.fromtimestamp(kline_data['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
            
            message = (
                f"{self.hook_prefix}"\
                f"K线形态提醒 - {symbol}({kline_data['close']:.4f})\n"
                f"周期: {period}\n"
                f"时间: {time_str}\n"
                f"形态: {pattern_type}\n"
                f"K线数据:\n"
                f"开盘: {kline_data['open']:.4f}\n"
                f"最高: {kline_data['high']:.4f}\n"
                f"最低: {kline_data['low']:.4f}\n"
                f"收盘: {kline_data['close']:.4f}\n"
                f"成交量: {kline_data['volume']}\n"
            )
            
            if additional_info:
                message += f"\n附加信息:\n{additional_info}"
            
            return self.message_publisher.send_string(message)
            
        except Exception as e:
            self.logger.error(f"发送形态提醒时发生错误: {e}")
            return False

    def send_long_shadow_alert(self, symbol: str, period: str, 
                            kline_data: dict, shadow_ratio: float,
                            shadow_type: str = "下影线") -> bool:
        """
        发送长影线提醒
        Args:
            symbol: 合约代码
            period: K线周期
            kline_data: K线数据
            shadow_ratio: 影线比例
            shadow_type: 影线类型（上影线/下影线）
        Returns:
            是否发送成功
        """
        try:
            time_str = datetime.fromtimestamp(kline_data['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                        
            message = (
                f"{self.hook_prefix}"\
                f"长{shadow_type}提醒 - {symbol}({kline_data['close']:.4f})\n"
                f"周期: {period}\n"
                f"时间: {time_str}\n"
                f"{shadow_type}比例: {shadow_ratio:.2f}\n"
                f"K线类型: {'阳线' if kline_data['close'] > kline_data['open'] else '阴线'}\n"
                f"K线数据:\n"
                f"开盘: {kline_data['open']:.4f}\n"
                f"最高: {kline_data['high']:.4f}\n"
                f"最低: {kline_data['low']:.4f}\n"
                f"收盘: {kline_data['close']:.4f}\n"
                f"成交量: {kline_data['volume']}"
            )
            
            return self.message_publisher.send_string(message)
            
        except Exception as e:
            self.logger.error(f"发送长影线提醒时发生错误: {e}")
            return False
        
    def send_order_status(self, message: str) -> bool:
        """
        发送订单状态信息
        Args:
            message: 订单状态信息字符串
        Returns:
            bool: 是否发送成功
        """
        try:
            # 如果原始消息已经有HOOK头且是DingRobot，保留它
            # 如果是LarkRobot，移除HOOK头
            if self.robot_type == 'l' and message.startswith("HOOK"):
                message = message[4:]
            elif self.robot_type == 'd' and not message.startswith("HOOK"):
                # 如果是DingRobot且消息没有HOOK头，根据需要添加
                # 这里假设订单状态消息不需要添加HOOK头
                pass
            
            # 发送消息
            return self.message_publisher.send_string(message)
            
        except Exception as e:
            self.logger.error(f"发送订单状态信息失败: {e}")
            return False

    def handle_commands_for_robot(self, robot_name, subscriber):
        """处理特定机器人的命令
        
        Args:
            robot_name: 机器人名称
            subscriber: 对应的ZMQ订阅器
        """
        self.logger.info(f"启动 {robot_name} 的命令处理线程")
        
        while True:
            try:
                # 接收ZMQ消息
                command_text = subscriber.recv_string()
                self.logger.info(f"从 {robot_name} 收到指令: {command_text}")
                
                # 处理命令
                self.dealCommand(command_text, robot_name)
                
            except Exception as e:
                self.logger.error(f"{robot_name} 处理命令时发生异常: {e}")

    def send_command_list(self, source_topic=None):
        """发送支持的指令列表"""
        command_list = """
支持的指令列表:
1. qs - 查询当前持仓
2. qo - 查询当前委托单
3. rq - 请求下一个请求ID
4. qh [品种] - 查询合约报价，例如: qh ES
5. ac [操作] [参数] - 交易相关操作:
   - ac b [品种] [类型] [数量] [价格] - 买入指令，例如: ac b ES LMT 1 4200
   - ac s [品种] [类型] [数量] [价格] - 卖出指令，例如: ac s ES LMT 1 4200
   - ac c [订单号] - 撤单指令，例如: ac c 123456
6. help 或 ? - 显示此帮助信息

指令类型说明:
- LMT: 限价单 (可以指定价格，也可以不指定价格，不指定时使用市场价格加减一定点数)
- MKT: 市价单 (不需要指定价格)
- STP: 止损单 (需要指定触发价格)
"""
        self.message_publisher.send_string(self.format_message("COMMAND_LIST", command_list, to_topic=source_topic))
        return

    def check_reconnect_time(self):
        """检查是否需要断开连接或重新连接"""
        while True:
            try:
                now = datetime.now(pytz.timezone('Asia/Shanghai'))
                
                # 判断是否是夏令时
                is_summer = self.is_dst(now.timestamp())
                end_hour = self.TRADING_HOURS['summer' if is_summer else 'winter']['end']
                
                # 计算当天的收盘时间
                closing_time = now.replace(
                    hour=end_hour,
                    minute=0,
                    second=0,
                    microsecond=0
                )
                
                # 计算断开连接和重新连接的时间
                disconnect_time = closing_time + timedelta(minutes=10)  # 收盘后10分钟断开
                reconnect_time = closing_time + timedelta(minutes=30)   # 收盘后30分钟重连
                
                # 检查是否需要断开连接
                if (now >= disconnect_time and now < reconnect_time and 
                    self.is_connected and 
                    (self.last_disconnect_date is None or 
                     self.last_disconnect_date.date() != now.date())):
                    
                    self.logger.info("到达预定断开时间，准备断开连接...")
                    self.disconnect_and_cleanup()
                    self.last_disconnect_date = now
                    self.is_connected = False
                
                # 检查是否需要重新连接
                elif (now >= reconnect_time and 
                      not self.is_connected and 
                      self.last_disconnect_date and 
                      self.last_disconnect_date.date() == now.date()):
                    
                    self.logger.info("到达预定重连时间，准备重新连接...")
                    self.reconnect()
                    self.is_connected = True
                
                # 每分钟检查一次
                time.sleep(60)
                
            except Exception as e:
                self.logger.error(f"检查重连时间时发生错误: {e}")
                time.sleep(60)  # 发生错误时等待1分钟后继续

    def disconnect_and_cleanup(self):
        """断开连接并清理资源"""
        try:
            # 取消所有行情订阅
            self.unsubscribe_market_data()
            
            # 断开IB连接
            self.app.disconnect()
            
            self.logger.info("已断开IB连接并清理资源")
            
            # 向钉钉和飞书机器人发送断开连接通知
            disconnect_msg = (
                "IB服务器即将进行每日重启维护，系统已断开连接。\n"
                "将在服务器重启完成后自动重连。\n"
                f"断开时间: {datetime.now(pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            # 发送给钉钉机器人
            if 'dingtalk' in self.config:
                self.message_publisher.send_string(
                    self.format_message("SYSTEM", disconnect_msg, to_topic=self.config['dingtalk'].get('topic', 'ding'))
                )
            
            # 发送给飞书机器人
            if 'lark' in self.config:
                self.message_publisher.send_string(
                    self.format_message("SYSTEM", disconnect_msg, to_topic=self.config['lark'].get('topic', 'lark'))
                )
            
        except Exception as e:
            self.logger.error(f"断开连接时发生错误: {e}")
            
            # 发送错误通知
            error_msg = (
                f"{self.hook_prefix}"
                "断开IB连接时发生错误！\n"
                f"错误信息: {str(e)}\n"
                f"时间: {datetime.now(pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            # 发送给钉钉机器人
            if 'dingtalk' in self.config:
                self.message_publisher.send_string(
                    self.format_message("ERROR", error_msg, to_topic=self.config['dingtalk'].get('topic', 'ding'))
                )
            
            # 发送给飞书机器人
            if 'lark' in self.config:
                self.message_publisher.send_string(
                    self.format_message("ERROR", error_msg, to_topic=self.config['lark'].get('topic', 'lark'))
                )

    def reconnect(self):
        """重新连接并恢复订阅"""
        try:
            # 重新连接
            self.connect()
            time.sleep(1)  # 等待连接建立
            
            # 重新加载合约
            self.load_contracts("Contracts")
            
            # 重新订阅行情
            self.subscribe_market_data(self.contracts)
            
            self.logger.info("已重新连接并恢复订阅")
            
            # 向钉钉和飞书机器人发送重连成功通知
            reconnect_msg = (
                f"{self.hook_prefix}"
                "IB服务器重启维护完成，系统已重新连接。\n"
                "已恢复所有合约订阅。\n"
                f"重连时间: {datetime.now(pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            # 发送给钉钉机器人
            if 'dingtalk' in self.config:
                self.message_publisher.send_string(
                    self.format_message("SYSTEM", reconnect_msg, to_topic=self.config['dingtalk'].get('topic', 'ding'))
                )
            
            # 发送给飞书机器人
            if 'lark' in self.config:
                self.message_publisher.send_string(
                    self.format_message("SYSTEM", reconnect_msg, to_topic=self.config['lark'].get('topic', 'lark'))
                )
            
        except Exception as e:
            self.logger.error(f"重新连接时发生错误: {e}")
            
            # 发送重连失败通知
            error_msg = (
                f"{self.hook_prefix}"
                "IB服务器重连失败！\n"
                f"错误信息: {str(e)}\n"
                f"时间: {datetime.now(pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')}\n"
                "系统将在1分钟后重试..."
            )
            
            # 发送给钉钉机器人
            if 'dingtalk' in self.config:
                self.message_publisher.send_string(
                    self.format_message("ERROR", error_msg, to_topic=self.config['dingtalk'].get('topic', 'ding'))
                )
            
            # 发送给飞书机器人
            if 'lark' in self.config:
                self.message_publisher.send_string(
                    self.format_message("ERROR", error_msg, to_topic=self.config['lark'].get('topic', 'lark'))
                )
            
            # 等待1分钟后重试
            time.sleep(60)
            self.reconnect()  # 递归调用自身进行重试

def main():
    
    logger = setup_logger()
    logger.info(f"启动 IBHelper")
    
    # 读取配置文件
    config = {}
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
            logger.info(f"已加载配置文件: config.json")
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
    
    # 创建IBHelper实例，传入config参数
    helper = IBHelper(config=config)
    helper.connect()
    time.sleep(1)
    
    helper.load_contracts("Contracts")
    
    # 启动IB API线程
    api_thread = threading.Thread(target=helper.run, daemon=True)
    api_thread.start()
    
    # 订阅市场数据
    helper.subscribe_market_data(helper.contracts)
    
    logger.info("IBHelper启动完成。")
    
    # 添加键盘中断处理
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("检测到 Ctrl+C，正在关闭 IBHelper...")
        helper.unsubscribe_market_data()
        helper.app.disconnect()
        logger.info("IBHelper 已关闭")

if __name__ == "__main__":
    main()